/**
 * 当前用户主播统计数据管理Hook
 * 
 * 专门用于主播用户查看自己的统计数据
 * 根据用户角色自动选择合适的API接口
 */

import { useState, useCallback } from 'react'
import { toast } from '@/hooks'
import { OperationsService } from '@/services/operations'
import { useAuthStore } from '@/stores/auth'
import { ROLES } from '@/constants'
import type { AnchorStatsResponse, FirstRechargeStatsResponse } from '@/pages/yunying/types/operations'

/**
 * 当前用户主播统计Hook状态接口
 */
interface UseCurrentUserAnchorStatsState {
  /** 主播统计数据 */
  statsData: AnchorStatsResponse | null
  /** 首充统计数据 */
  firstRechargeData: FirstRechargeStatsResponse | null
  /** 统计数据加载状态 */
  statsLoading: boolean
  /** 首充数据加载状态 */
  firstRechargeLoading: boolean
  /** 错误信息 */
  error: string | null
  /** 时间范围 */
  timeRange: {
    startTime?: number
    endTime?: number
  }
}

/**
 * 当前用户主播统计数据管理Hook
 * 
 * 根据用户角色自动选择合适的API接口：
 * - 主播角色：调用 /operations/profile/stats（需要ID转换）
 * - 管理员角色：调用 /operations/anchors/{anchorId}/stats（直接使用vim_user ID）
 */
export function useCurrentUserAnchorStats() {
  const { roles } = useAuthStore()
  const [state, setState] = useState<UseCurrentUserAnchorStatsState>({
    statsData: null,
    firstRechargeData: null,
    statsLoading: false,
    firstRechargeLoading: false,
    error: null,
    timeRange: {}
  })

  // 检查当前用户是否是主播角色
  const isAnchorUser = roles?.includes(ROLES.ANCHOR) || false
  
  /**
   * 加载主播统计数据
   * 根据用户角色选择不同的API接口
   */
  const loadStats = useCallback(async (
    anchorId?: number, // 管理员角色需要传入anchorId，主播角色可以不传
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      statsLoading: true, 
      error: null,
      timeRange: { startTime, endTime }
    }))
    
    try {
      let data: AnchorStatsResponse
      
      if (isAnchorUser) {
        // 主播用户：调用当前用户的统计接口（自动使用当前登录用户的sys_user ID）
        data = await OperationsService.getCurrentUserAnchorStats(startTime, endTime)
      } else {
        // 管理员用户：调用指定主播的统计接口（需要传入vim_user ID）
        if (!anchorId) {
          throw new Error('管理员查看主播统计需要提供主播ID')
        }
        data = await OperationsService.getAnchorStats(anchorId, startTime, endTime)
      }
      
      setState(prev => ({
        ...prev,
        statsData: data,
        statsLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取主播统计数据失败'
      setState(prev => ({
        ...prev,
        statsData: null,
        statsLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [isAnchorUser])
  
  /**
   * 加载首充统计数据
   * 根据用户角色选择不同的API接口
   */
  const loadFirstRechargeStats = useCallback(async (
    anchorId?: number, // 管理员角色需要传入anchorId，主播角色可以不传
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      firstRechargeLoading: true, 
      error: null,
      timeRange: { startTime, endTime }
    }))
    
    try {
      let data: FirstRechargeStatsResponse
      
      if (isAnchorUser) {
        // 主播用户：调用当前用户的首充统计接口（自动使用当前登录用户的sys_user ID）
        data = await OperationsService.getCurrentUserFirstRechargeStats(startTime, endTime)
      } else {
        // 管理员用户：调用指定主播的首充统计接口（需要传入vim_user ID）
        if (!anchorId) {
          throw new Error('管理员查看主播首充统计需要提供主播ID')
        }
        data = await OperationsService.getFirstRechargeStats(anchorId, startTime, endTime)
      }
      
      setState(prev => ({
        ...prev,
        firstRechargeData: data,
        firstRechargeLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取首充统计数据失败'
      setState(prev => ({
        ...prev,
        firstRechargeData: null,
        firstRechargeLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [isAnchorUser])

  /**
   * 刷新所有数据
   */
  const refreshAll = useCallback(async (
    anchorId?: number,
    startTime?: number,
    endTime?: number
  ) => {
    await Promise.all([
      loadStats(anchorId, startTime, endTime),
      loadFirstRechargeStats(anchorId, startTime, endTime)
    ])
  }, [loadStats, loadFirstRechargeStats])

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  return {
    // 状态
    ...state,
    isAnchorUser,
    
    // 方法
    loadStats,
    loadFirstRechargeStats,
    refreshAll,
    clearError
  }
}

/**
 * 统计数据格式化Hook
 * 复用原有的格式化逻辑
 */
export function useStatsFormatter() {
  const formatCurrency = (amount: number | string): string => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(isNaN(num) ? 0 : num)
  }

  const formatPercentage = (value: number | string): string => {
    const num = typeof value === 'string' ? parseFloat(value) : value
    return `${(isNaN(num) ? 0 : num).toFixed(2)}%`
  }

  const formatNumber = (value: number | string): string => {
    const num = typeof value === 'string' ? parseFloat(value) : value
    return new Intl.NumberFormat('zh-CN').format(isNaN(num) ? 0 : num)
  }

  return {
    formatCurrency,
    formatPercentage,
    formatNumber
  }
}
