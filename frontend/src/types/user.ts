/**
 * 用户管理相关类型定义
 */

import type { PageRequest } from './api'

/**
 * 用户信息
 */
export interface User {
  id: number
  username: string
  nickname?: string
  realName?: string
  avatar?: string
  email?: string
  phone?: string
  gender?: number
  birthday?: string
  status: number
  isAdmin: number
  deptId?: number  // 用户主部门ID
  deptName?: string  // 用户主部门名称（用于显示）
  deptIds?: number[]  // 用户所属的所有部门ID列表
  deptNames?: string[]  // 用户所属的所有部门名称列表
  roleIds?: number[]  // 用户角色ID列表
  roleNames?: string[]  // 用户角色名称列表
  permissions?: string[]  // 用户权限编码列表
  lastLoginTime?: string
  lastLoginIp?: string
  passwordUpdateTime?: string
  remark?: string
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  deleted: number
  tenantId: number  // 租户ID必填，与后端保持一致
}

/**
 * 用户查询请求
 */
export interface UserQueryRequest extends PageRequest {
  username?: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  gender?: number
  status?: number
  isAdmin?: number
  roleId?: number
  roleCode?: string
  startTime?: string
  endTime?: string
}

/**
 * 用户创建请求
 */
export interface UserCreateRequest {
  username: string
  password: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  gender?: number
  birthday?: string
  status: number
  deptId: number  // 部门ID，必填
  remark?: string
  roleIds?: number[]
}

/**
 * 用户更新请求
 */
export interface UserUpdateRequest {
  id: number
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  gender?: number
  birthday?: string
  status: number
  deptId?: number  // 部门ID，可选
  remark?: string
  roleIds?: number[]
}

/**
 * 重置密码请求
 */
export interface ResetPasswordRequest {
  id: number
  newPassword: string
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1   // 启用
}

/**
 * 性别枚举
 */
export enum Gender {
  UNKNOWN = 0, // 未知
  MALE = 1,    // 男
  FEMALE = 2   // 女
}

/**
 * 管理员标识枚举
 */
export enum AdminFlag {
  NO = 0,  // 非管理员
  YES = 1  // 管理员
}

/**
 * 用户状态选项
 */
export const USER_STATUS_OPTIONS = [
  { label: '启用', value: UserStatus.ENABLED },
  { label: '禁用', value: UserStatus.DISABLED }
]

/**
 * 性别选项
 */
export const GENDER_OPTIONS = [
  { label: '未知', value: Gender.UNKNOWN },
  { label: '男', value: Gender.MALE },
  { label: '女', value: Gender.FEMALE }
]

/**
 * 管理员标识选项
 */
export const ADMIN_FLAG_OPTIONS = [
  { label: '普通用户', value: AdminFlag.NO },
  { label: '管理员', value: AdminFlag.YES }
]

/**
 * 获取用户状态标签
 */
export const getUserStatusLabel = (status: number): string => {
  const option = USER_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || '未知'
}

/**
 * 获取性别标签
 */
export const getGenderLabel = (gender: number): string => {
  const option = GENDER_OPTIONS.find(opt => opt.value === gender)
  return option?.label || '未知'
}

/**
 * 获取管理员标识标签
 */
export const getAdminFlagLabel = (isAdmin: number): string => {
  const option = ADMIN_FLAG_OPTIONS.find(opt => opt.value === isAdmin)
  return option?.label || '未知'
}
