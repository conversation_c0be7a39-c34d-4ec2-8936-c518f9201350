package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 用户查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户查询请求")
public class UserQueryRequest extends PageQuery {
    
    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称", example = "管理员")
    private String nickname;
    
    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名", example = "张三")
    private String realName;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13800138000")
    private String phone;
    
    /**
     * 性别（0-未知，1-男，2-女）
     */
    @Schema(description = "性别", example = "1")
    private Integer gender;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    /**
     * 部门ID
     */
    @Schema(description = "部门ID", example = "1")
    private Long deptId;
    
    /**
     * 是否管理员（0-否，1-是）
     */
    @Schema(description = "是否管理员", example = "1")
    private Integer isAdmin;

    /**
     * 角色ID（按角色筛选用户）
     */
    @Schema(description = "角色ID", example = "1")
    private Long roleId;

    /**
     * 角色编码（按角色编码筛选用户）
     */
    @Schema(description = "角色编码", example = "ADMIN")
    private String roleCode;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime createTimeEnd;
}
