package com.jcloud.admin.service.impl;

import com.jcloud.admin.constant.OperationsConstants;
import com.jcloud.admin.dto.request.AnchorQueryRequest;
import com.jcloud.admin.dto.request.ConsumeQueryRequest;
import com.jcloud.admin.dto.request.RechargeQueryRequest;
import com.jcloud.admin.dto.request.SubUserQueryRequest;
import com.jcloud.admin.dto.response.*;
import com.jcloud.admin.mapper.OperationsMapper;
import com.jcloud.admin.service.DeptPermissionService;
import com.jcloud.admin.service.OperationsService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.DataMaskingUtils;
import com.jcloud.common.util.RoleCodeUtils;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.util.TimeUtil;
import com.mybatisflex.core.datasource.DataSourceKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 运营数据服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationsServiceImpl implements OperationsService {

    private final OperationsMapper operationsMapper;
    private final SysUserService sysUserService;
    private final DeptPermissionService deptPermissionService;

    /**
     * 确保整个方法使用slave数据源
     *
     * @param request 查询请求参数
     * @return 分页的主播列表
     */
    @Override
    public PageResult<AnchorListResponse> getAnchorList(AnchorQueryRequest request) {
        log.info("开始查询主播列表，查询条件：{}", request);
        try {
            // 参数校验
            validatePageQuery(request);

            // 应用数据权限过滤
            applyDataPermissionFilter(request);

            log.debug("开始手动分页查询：pageNum={}, pageSize={}, offset={}",
                    request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countAnchorList(request);
            log.debug("查询到总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("主播列表查询完成，总记录数为0");
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<AnchorListResponse> records = operationsMapper.selectAnchorList(request);
            log.debug("查询到当前页记录数：{}", records != null ? records.size() : 0);
            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，返回空结果");
                records = List.of();
            }
            // 数据处理
            processAnchorListData(records);

            log.info("主播列表查询完成，当前页记录数：{}，总记录数：{}", records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询主播列表时发生数据库异常", e);
            throw new BusinessException("查询主播列表失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询主播列表时发生未知异常", e);
            throw new BusinessException("查询主播列表失败");
        }
    }

    @Override
    public AnchorStatsResponse getAnchorStatsBySysUserId(Long sysUserId, Integer startTime, Integer endTime) {
        log.info("开始通过sys_user ID获取主播统计数据，sys_user ID：{}，时间范围：{} - {}", sysUserId, startTime, endTime);

        try {
            // 参数校验
            if (sysUserId == null || sysUserId <= 0) {
                throw new BusinessException("sys_user ID不能为空");
            }

            // 将sys_user ID转换为vim_user ID
            Integer vimUserId = getVimUserIdBySysUserId(sysUserId);
            if (vimUserId == null) {
                log.warn("未找到sys_user ID {} 对应的vim_user记录", sysUserId);
                throw new BusinessException("未找到对应的主播记录，请联系管理员");
            }

            log.info("ID转换成功：sys_user ID {} -> vim_user ID {}", sysUserId, vimUserId);

            // 调用原有的方法获取统计数据
            return getAnchorStats(vimUserId, startTime, endTime);

        } catch (Exception e) {
            log.error("通过sys_user ID获取主播统计数据失败，sys_user ID：{}", sysUserId, e);
            throw e;
        }
    }

    @Override
    public AnchorStatsResponse getAnchorStats(Integer anchorId, Integer startTime, Integer endTime) {
        log.info("开始获取主播统计数据，vim_user ID：{}，时间范围：{} - {}", anchorId, startTime, endTime);
        try {
            // 参数校验
            if (anchorId == null || anchorId <= 0) {
                throw new BusinessException("主播ID不能为空");
            }

            // 获取当前用户的查询层数
            Integer maxLevel = getCurrentUserQueryLevel();
            log.info("当前用户查询层数：{}", maxLevel);

            // 确定时间范围并转换为字符串格式
            String startTimeStr;
            String endTimeStr;

            if (startTime != null && endTime != null) {
                startTimeStr = startTime.toString();
                endTimeStr = endTime.toString();
            } else {
                // 默认使用当前月份的时间范围
                Long now = TimeUtil.now();
                String currentDate = TimeUtil.formatDate(now);
                String monthStart = currentDate.substring(0, 8) + "01";
                Long startDateTime = TimeUtil.getDayStart(monthStart);
                Long endDateTime = TimeUtil.getDayEnd(currentDate);
                startTimeStr = startDateTime.toString();
                endTimeStr = endDateTime.toString();
                startTime = startDateTime.intValue();
                endTime = endDateTime.intValue();
            }

            log.info("调用GetSubordinateProfit存储过程: anchorId={}, maxLevel={}, startTime={}, endTime={}",
                    anchorId, maxLevel, startTimeStr, endTimeStr);

            // 调用GetSubordinateProfit存储过程获取统计数据
            SubordinateProfit subordinateProfit = operationsMapper.getSubordinateProfit(
                    anchorId,
                    maxLevel,
                    startTimeStr,
                    endTimeStr
            );

            if (subordinateProfit == null) {
                log.warn("未找到主播ID为 {} 的统计数据", anchorId);
                return createEmptyAnchorStats(startTime, endTime);
            }

            // 将SubordinateProfit转换为AnchorStatsResponse
            AnchorStatsResponse stats = convertToAnchorStatsResponse(subordinateProfit, startTime, endTime);

            log.info("主播统计数据获取完成，主播ID：{}，总用户数：{}，总充值：{}，总利润：{}",
                    anchorId, stats.getUserCount(), stats.getTotalRecharge(), stats.getActualProfit());
            return stats;

        } catch (DataAccessException e) {
            log.error("获取主播统计数据时发生数据库异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播统计数据失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取主播统计数据时发生未知异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播统计数据失败");
        }
    }

    @Override
    public FirstRechargeStatsResponse getFirstRechargeStatsBySysUserId(Long sysUserId, Integer startTime, Integer endTime) {
        log.info("开始通过sys_user ID获取主播首充统计数据，sys_user ID：{}，时间范围：{} - {}", sysUserId, startTime, endTime);

        try {
            // 参数校验
            if (sysUserId == null || sysUserId <= 0) {
                throw new BusinessException("sys_user ID不能为空");
            }

            // 将sys_user ID转换为vim_user ID
            Integer vimUserId = getVimUserIdBySysUserId(sysUserId);
            if (vimUserId == null) {
                log.warn("未找到sys_user ID {} 对应的vim_user记录", sysUserId);
                throw new BusinessException("未找到对应的主播记录，请联系管理员");
            }

            log.info("ID转换成功：sys_user ID {} -> vim_user ID {}", sysUserId, vimUserId);

            // 调用原有的方法获取首充统计数据
            return getFirstRechargeStats(vimUserId, startTime, endTime);

        } catch (Exception e) {
            log.error("通过sys_user ID获取主播首充统计数据失败，sys_user ID：{}", sysUserId, e);
            throw e;
        }
    }

    @Override
    public FirstRechargeStatsResponse getFirstRechargeStats(Integer anchorId, Integer startTime, Integer endTime) {
        log.info("开始获取主播首充统计数据，vim_user ID：{}，时间范围：{} - {}", anchorId, startTime, endTime);

        try {
            // 参数校验
            if (anchorId == null || anchorId <= 0) {
                throw new BusinessException("主播ID不能为空");
            }

            // 获取首充统计数据
            FirstRechargeStatsResponse stats = operationsMapper.getFirstRechargeStats(anchorId, startTime, endTime);

            if (stats == null) {
                log.warn("未找到主播ID为 {} 的首充统计数据", anchorId);
                return createEmptyFirstRechargeStats(anchorId, startTime, endTime);
            }

            // 数据处理
            processFirstRechargeStatsData(stats);

            log.info("主播首充统计数据获取完成，主播ID：{}", anchorId);
            return stats;

        } catch (DataAccessException e) {
            log.error("获取主播首充统计数据时发生数据库异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播首充统计数据失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取主播首充统计数据时发生未知异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播首充统计数据失败");
        }
    }

    @Override
    public PageResult<SubUserResponse> getSubUsers(Integer anchorId, SubUserQueryRequest request) {
        log.info("开始查询主播下级用户列表，主播ID：{}，查询条件：{}", anchorId, request);

        try {
            // 参数校验
            if (anchorId == null || anchorId <= 0) {
                throw new BusinessException("主播ID不能为空");
            }
            validatePageQuery(request);

            log.debug("开始手动分页查询下级用户：主播ID={}, pageNum={}, pageSize={}, offset={}",
                    anchorId, request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countSubUsers(anchorId, request);
            log.debug("查询到下级用户总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("主播下级用户列表查询完成，主播ID：{}，总记录数为0", anchorId);
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<SubUserResponse> records = operationsMapper.selectSubUsers(anchorId, request);
            log.debug("查询到下级用户当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，主播ID：{}，返回空结果", anchorId);
                records = List.of();
            }

            // 数据处理
            processSubUserData(records);

            log.info("主播下级用户列表查询完成，主播ID：{}，当前页记录数：{}，总记录数：{}",
                    anchorId, records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询主播下级用户列表时发生数据库异常，主播ID：{}", anchorId, e);
            throw new BusinessException("查询主播下级用户列表失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询主播下级用户列表时发生未知异常，主播ID：{}", anchorId, e);
            throw new BusinessException("查询主播下级用户列表失败");
        }
    }

    @Override
    public PageResult<ConsumeDetailResponse> getUserConsumeDetails(Integer userId, ConsumeQueryRequest request) {
        log.info("开始查询用户消费详情，用户ID：{}，查询条件：{}", userId, request);

        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new BusinessException("用户ID不能为空");
            }
            validatePageQuery(request);

            log.debug("开始手动分页查询消费详情：用户ID={}, pageNum={}, pageSize={}, offset={}",
                    userId, request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countConsumeDetails(userId, request);
            log.debug("查询到消费详情总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("用户消费详情查询完成，用户ID：{}，总记录数为0", userId);
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<ConsumeDetailResponse> records = operationsMapper.selectConsumeDetails(userId, request);
            log.debug("查询到消费详情当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，用户ID：{}，返回空结果", userId);
                records = List.of();
            }

            // 数据处理
            processConsumeDetailData(records);

            log.info("用户消费详情查询完成，用户ID：{}，当前页记录数：{}，总记录数：{}",
                    userId, records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询用户消费详情时发生数据库异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户消费详情失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询用户消费详情时发生未知异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户消费详情失败");
        }
    }

    @Override
    public PageResult<RechargeDetailResponse> getUserRechargeDetails(Integer userId, RechargeQueryRequest request) {
        log.info("开始查询用户充值详情，用户ID：{}，查询条件：{}", userId, request);

        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new BusinessException("用户ID不能为空");
            }
            validatePageQuery(request);

            log.debug("开始手动分页查询充值详情：用户ID={}, pageNum={}, pageSize={}, offset={}",
                    userId, request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countRechargeDetails(userId, request);
            log.debug("查询到充值详情总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("用户充值详情查询完成，用户ID：{}，总记录数为0", userId);
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<RechargeDetailResponse> records = operationsMapper.selectRechargeDetails(userId, request);
            log.debug("查询到充值详情当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，用户ID：{}，返回空结果", userId);
                records = List.of();
            }

            // 数据处理
            processRechargeDetailData(records);

            log.info("用户充值详情查询完成，用户ID：{}，当前页记录数：{}，总记录数：{}",
                    userId, records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询用户充值详情时发生数据库异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户充值详情失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询用户充值详情时发生未知异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户充值详情失败");
        }
    }

    /**
     * 校验分页查询参数
     */
    private void validatePageQuery(com.jcloud.common.page.PageQuery pageQuery) {
        if (pageQuery.getPageNum() == null || pageQuery.getPageNum() < 1) {
            pageQuery.setPageNum(OperationsConstants.Page.DEFAULT_PAGE_NUM);
        }
        if (pageQuery.getPageSize() == null || pageQuery.getPageSize() < 1) {
            pageQuery.setPageSize(OperationsConstants.Page.DEFAULT_PAGE_SIZE);
        }
        if (pageQuery.getPageSize() > OperationsConstants.Page.MAX_PAGE_SIZE) {
            pageQuery.setPageSize(OperationsConstants.Page.MAX_PAGE_SIZE);
        }
    }

    /**
     * 处理主播列表数据
     */
    private void processAnchorListData(List<AnchorListResponse> anchors) {
        if (anchors == null || anchors.isEmpty()) {
            return;
        }

        for (AnchorListResponse anchor : anchors) {
            // 确保字符串字段不为null并进行脱敏处理
            if (anchor.getNickname() == null) {
                anchor.setNickname("未设置昵称");
            }
            if (anchor.getUsername() == null) {
                anchor.setUsername("未设置用户名");
            }
            if (anchor.getPhone() == null) {
                anchor.setPhone("未绑定手机");
            } else {
                // 对主播手机号进行脱敏处理
                anchor.setPhone(DataMaskingUtils.maskPhone(anchor.getPhone()));
            }
            if (anchor.getUserimage() == null) {
                anchor.setUserimage("");
            }
            if (anchor.getInviteCode() == null) {
                anchor.setInviteCode("");
            }
            if (anchor.getLastLoginIp() == null) {
                anchor.setLastLoginIp("");
            }

            // 确保数值字段不为null
            if (anchor.getCoin() == null) {
                anchor.setCoin(BigDecimal.ZERO);
            }
            if (anchor.getKey() == null) {
                anchor.setKey(BigDecimal.ZERO);
            }
            if (anchor.getSubUserCount() == null) {
                anchor.setSubUserCount(0);
            }
        }
    }

    /**
     * 处理主播统计数据
     */
    private void processAnchorStatsData(AnchorStatsResponse stats) {
        if (stats == null) {
            return;
        }

        // 确保数值字段不为null
        if (stats.getTotalRecharge() == null) {
            stats.setTotalRecharge(BigDecimal.ZERO);
        }
        if (stats.getTotalConsume() == null) {
            stats.setTotalConsume(BigDecimal.ZERO);
        }
        if (stats.getTotalClaimAmount() == null) {
            stats.setTotalClaimAmount(BigDecimal.ZERO);
        }
        if (stats.getTotalShippedAmount() == null) {
            stats.setTotalShippedAmount(BigDecimal.ZERO);
        }
        if (stats.getTotalBackpackAmount() == null) {
            stats.setTotalBackpackAmount(BigDecimal.ZERO);
        }
        if (stats.getPeriodTotalRecharge() == null) {
            stats.setPeriodTotalRecharge(BigDecimal.ZERO);
        }
        if (stats.getTotalTurnover() == null) {
            stats.setTotalTurnover(BigDecimal.ZERO);
        }
        // 计算实际利润和利润比
        calculateProfitData(stats);
    }

    /**
     * 计算利润相关数据
     */
    private void calculateProfitData(AnchorStatsResponse stats) {
        BigDecimal totalConsume = stats.getTotalConsume();
        BigDecimal totalShipped = stats.getTotalShippedAmount();

        if (totalConsume != null && totalShipped != null) {
            // 实际利润 = 总消费金额 - 总实际发货金额
            BigDecimal actualProfit = totalConsume.subtract(totalShipped);
            stats.setActualProfit(actualProfit);

            // 利润比 = 实际利润 / 总消费金额 × 100%
            if (totalConsume.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal profitRatio = actualProfit.divide(totalConsume, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                stats.setProfitRatio(profitRatio);
            } else {
                stats.setProfitRatio(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 处理首充统计数据
     */
    private void processFirstRechargeStatsData(FirstRechargeStatsResponse stats) {
        if (stats == null) {
            return;
        }

        // 确保数值字段不为null
        if (stats.getFirstRechargeUserCount() == null) {
            stats.setFirstRechargeUserCount(0);
        }
        if (stats.getTotalSubUserCount() == null) {
            stats.setTotalSubUserCount(0);
        }
        if (stats.getTotalFirstRechargeAmount() == null) {
            stats.setTotalFirstRechargeAmount(BigDecimal.ZERO);
        }
        if (stats.getPeriodFirstRechargeAmount() == null) {
            stats.setPeriodFirstRechargeAmount(BigDecimal.ZERO);
        }

        // 重新计算转化率和平均金额，确保精度
        recalculateFirstRechargeStats(stats);
    }

    /**
     * 重新计算首充统计数据
     */
    private void recalculateFirstRechargeStats(FirstRechargeStatsResponse stats) {
        // 计算首充转化率
        if (stats.getTotalSubUserCount() > 0) {
            BigDecimal conversionRate = new BigDecimal(stats.getFirstRechargeUserCount())
                    .divide(new BigDecimal(stats.getTotalSubUserCount()), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            stats.setFirstRechargeConversionRate(conversionRate);
        } else {
            stats.setFirstRechargeConversionRate(BigDecimal.ZERO);
        }

        // 计算平均首充金额
        if (stats.getFirstRechargeUserCount() > 0) {
            BigDecimal avgAmount = stats.getTotalFirstRechargeAmount()
                    .divide(new BigDecimal(stats.getFirstRechargeUserCount()), 2, RoundingMode.HALF_UP);
            stats.setAvgFirstRechargeAmount(avgAmount);
        } else {
            stats.setAvgFirstRechargeAmount(BigDecimal.ZERO);
        }

        // 计算时间区间内平均首充金额
        if (stats.getPeriodFirstRechargeUserCount() != null && stats.getPeriodFirstRechargeUserCount() > 0) {
            BigDecimal periodAvgAmount = stats.getPeriodFirstRechargeAmount()
                    .divide(new BigDecimal(stats.getPeriodFirstRechargeUserCount()), 2, RoundingMode.HALF_UP);
            stats.setPeriodAvgFirstRechargeAmount(periodAvgAmount);
        } else {
            stats.setPeriodAvgFirstRechargeAmount(BigDecimal.ZERO);
        }
    }

    /**
     * 处理下级用户数据
     */
    private void processSubUserData(List<SubUserResponse> users) {
        if (users == null || users.isEmpty()) {
            return;
        }

        for (SubUserResponse user : users) {
            // 确保字符串字段不为null
            if (user.getNickname() == null) {
                user.setNickname("未设置昵称");
            }
            if (user.getUsername() == null) {
                user.setUsername("未设置用户名");
            }
            if (user.getPhone() == null) {
                user.setPhone("未绑定手机");
            } else {
                // 对手机号进行脱敏处理
                user.setPhone(DataMaskingUtils.maskPhone(user.getPhone()));
            }
            if (user.getUserimage() == null) {
                user.setUserimage("");
            }

            // 确保数值字段不为null
            if (user.getCoin() == null) {
                user.setCoin(BigDecimal.ZERO);
            }
            if (user.getKey() == null) {
                user.setKey(BigDecimal.ZERO);
            }
            if (user.getTotalRecharge() == null) {
                user.setTotalRecharge(BigDecimal.ZERO);
            }
            if (user.getTotalConsume() == null) {
                user.setTotalConsume(BigDecimal.ZERO);
            }
            if (user.getFirstRechargeAmount() == null) {
                user.setFirstRechargeAmount(BigDecimal.ZERO);
            }
            if (user.getHasFirstRecharge() == null) {
                user.setHasFirstRecharge(false);
            }

            // 确保整数字段不为null
            if (user.getState() == null) {
                user.setState(1); // 默认正常状态
            }
            if (user.getIsauth() == null) {
                user.setIsauth(0); // 默认未实名
            }
            if (user.getLevel() == null) {
                user.setLevel(1); // 默认等级1
            }
            if (user.getExp() == null) {
                user.setExp(BigDecimal.ZERO); // 默认经验0
            }
        }
    }

    /**
     * 处理消费详情数据
     */
    private void processConsumeDetailData(List<ConsumeDetailResponse> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        for (ConsumeDetailResponse detail : details) {
            // 确保字符串字段不为null
            if (detail.getInfo() == null) {
                detail.setInfo("无消费说明");
            }

            // 推断消费类型
            if (detail.getConsumeType() == null) {
                String consumeType = OperationsConstants.ConsumeType.inferType(detail.getInfo());
                detail.setConsumeType(consumeType);
            }

            // 确保数值字段不为null
            if (detail.getAmount() == null) {
                detail.setAmount(BigDecimal.ZERO);
            }
            if (detail.getBalance() == null) {
                detail.setBalance(BigDecimal.ZERO);
            }
            if (detail.getIsAfterFirstRecharge() == null) {
                detail.setIsAfterFirstRecharge(false);
            }

            // 确保时间字段不为null
            if (detail.getTime() == null) {
                detail.setTime(0); // 设置为0时间戳
            }
        }
    }

    /**
     * 处理充值详情数据
     */
    private void processRechargeDetailData(List<RechargeDetailResponse> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        for (RechargeDetailResponse detail : details) {
            // 设置状态描述
            if (detail.getStateDesc() == null) {
                String stateDesc = OperationsConstants.RechargeState.getStateDesc(detail.getState());
                detail.setStateDesc(stateDesc);
            }

            // 推断支付方式
            if (detail.getPaymentMethod() == null) {
                String paymentMethod = OperationsConstants.PaymentMethod.inferMethod(detail.getPayid());
                detail.setPaymentMethod(paymentMethod);
            }

            // 确保数值字段不为null
            if (detail.getAmount() == null) {
                detail.setAmount(BigDecimal.ZERO);
            }
            if (detail.getCoin() == null) {
                detail.setCoin(BigDecimal.ZERO);
            }
            if (detail.getIsFirstRecharge() == null) {
                detail.setIsFirstRecharge(false);
            }
        }
    }

    /**
     * 创建空的主播统计数据
     */
    private AnchorStatsResponse createEmptyAnchorStats(Integer startTime, Integer endTime) {
        AnchorStatsResponse stats = new AnchorStatsResponse();
        stats.setTotalRecharge(BigDecimal.ZERO);
        stats.setTotalConsume(BigDecimal.ZERO);
        stats.setUserCount(0);
        stats.setPeriodNewUserCount(0);
        stats.setTotalClaimAmount(BigDecimal.ZERO);
        stats.setTotalShippedAmount(BigDecimal.ZERO);
        stats.setTotalBackpackAmount(BigDecimal.ZERO);
        stats.setPeriodTotalRecharge(BigDecimal.ZERO);
        stats.setTotalTurnover(BigDecimal.ZERO);
        stats.setProfitRatio(BigDecimal.ZERO);
        stats.setActualProfit(BigDecimal.ZERO);
        stats.setStartTime(startTime);
        stats.setEndTime(endTime);
        return stats;
    }

    /**
     * 创建空的首充统计数据
     */
    private FirstRechargeStatsResponse createEmptyFirstRechargeStats(Integer anchorId, Integer startTime, Integer endTime) {
        FirstRechargeStatsResponse stats = new FirstRechargeStatsResponse();
        stats.setFirstRechargeUserCount(0);
        stats.setTotalSubUserCount(0);
        stats.setFirstRechargeConversionRate(BigDecimal.ZERO);
        stats.setTotalFirstRechargeAmount(BigDecimal.ZERO);
        stats.setAvgFirstRechargeAmount(BigDecimal.ZERO);
        stats.setPeriodFirstRechargeUserCount(0);
        stats.setPeriodFirstRechargeAmount(BigDecimal.ZERO);
        stats.setPeriodAvgFirstRechargeAmount(BigDecimal.ZERO);
        stats.setStartTime(startTime);
        stats.setEndTime(endTime);
        return stats;
    }

    @Override
    public SubordinateProfit getOperationsStats(Integer startTime, Integer endTime) {
        log.info("开始获取运营统计数据，时间范围：{} - {}", startTime, endTime);
        try {
            // 获取当前用户ID作为根用户ID
            Long currentUserId = SecurityUtils.getUserId();
            if (currentUserId == null) {
                throw new BusinessException("用户未登录");
            }

            // 检查是否为超级管理员（ID为1的用户就是超级管理员）
            boolean isSuperAdmin = currentUserId.equals(1L);
            Integer vimUserId = null;


            if (isSuperAdmin) {
                // 超级管理员标识
                vimUserId = 0;
                log.info("✅ 检测到超级管理员登录（ID=1），将显示所有统计数据");
            } else {
                // 普通用户需要根据sys_user的ID获取对应的vim_user的ID
                vimUserId = getVimUserIdBySysUserId(currentUserId);
                if (vimUserId == null) {
                    throw new BusinessException("未找到对应的vim_user记录，请联系管理员");
                }
            }

            // 确定时间范围并转换为字符串格式
            String startTimeStr;
            String endTimeStr;

            if (startTime != null && endTime != null) {
                startTimeStr = startTime.toString();
                endTimeStr = endTime.toString();
            } else {
                // 默认使用当前月份的时间范围
                Long now = TimeUtil.now();
                String currentDate = TimeUtil.formatDate(now);
                String monthStart = currentDate.substring(0, 8) + "01";
                Long startDateTime = TimeUtil.getDayStart(monthStart);
                Long endDateTime = TimeUtil.getDayEnd(currentDate);
                startTimeStr = startDateTime.toString();
                endTimeStr = endDateTime.toString();
            }

            // 获取用户查询层级限制
            Integer maxLevel;
            if (isSuperAdmin) {
                // 超级管理员使用0表示查询所有数据
                maxLevel = 0;
                log.info("超级管理员模式，maxLevel设置为0，将查询所有主播的下级用户数据");
            } else {
                maxLevel = getCurrentUserQueryLevel();
            }

            if (isSuperAdmin) {
                log.info("尝试调用GetSubordinateProfit存储过程（超级管理员模式）: rootUid={}, maxLevel={}, startTime={}, endTime={}",
                        vimUserId, maxLevel, startTimeStr, endTimeStr);
            } else {
                log.info("尝试调用GetSubordinateProfit存储过程: rootUid={} (vim_user_id), maxLevel={}, startTime={}, endTime={}",
                        vimUserId, maxLevel, startTimeStr, endTimeStr);
            }
            SubordinateProfit result;

            try {
                // 尝试调用GetSubordinateProfit存储过程，使用vim_user的ID
                result = operationsMapper.getSubordinateProfit(
                        vimUserId,
                        maxLevel,
                        startTimeStr,
                        endTimeStr
                );

                if (result != null) {
                    log.info("存储过程调用成功，返回结果: {}", result);
                } else {
                    log.warn("存储过程返回空结果，使用默认值");
                    result = createEmptySubordinateProfit();
                }

            } catch (Exception e) {
                log.warn("调用GetSubordinateProfit存储过程失败，使用备用查询方法: {}", e.getMessage());

                // 备用方案：使用现有的查询方法
                result = new SubordinateProfit();

                try {
                    // 获取总充值金额（使用现有的查询方法）
                    Long startDateTime = Long.parseLong(startTimeStr);
                    Long endDateTime = Long.parseLong(endTimeStr);
                    LocalDateTime startLocalDateTime = TimeUtil.toLocalDateTime(startDateTime);
                    LocalDateTime endLocalDateTime = TimeUtil.toLocalDateTime(endDateTime);

                    BigDecimal totalRecharge = operationsMapper.getTotalRechargeAmount(startLocalDateTime, endLocalDateTime, maxLevel);
                    result.setZongchongzhi(totalRecharge != null ? totalRecharge : BigDecimal.ZERO);

                    // 获取总消费金额（使用现有的查询方法）
                    BigDecimal totalConsume = operationsMapper.getTotalConsumeAmount(startLocalDateTime, endLocalDateTime, maxLevel);

                    // 计算总利润（总充值 - 总消费）
                    BigDecimal totalProfit = result.getZongchongzhi().subtract(totalConsume != null ? totalConsume : BigDecimal.ZERO);
                    result.setZonglirun(totalProfit);

                    // 设置总用户数（暂时使用固定值）
                    result.setZongyonghu("0");

                } catch (Exception fallbackException) {
                    log.error("备用查询方法也失败，返回空统计数据: {}", fallbackException.getMessage());
                    result = createEmptySubordinateProfit();
                }
            }

            log.info("运营统计数据获取完成: 总用户数={}, 总充值={}, 总利润={}",
                    result.getZongyonghu(), result.getZongchongzhi(), result.getZonglirun());
            return result;

        } catch (Exception e) {
            log.error("获取运营统计数据失败", e);
            throw new BusinessException("获取运营统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取今日充值金额
     */
    private BigDecimal getTodayRechargeAmount() {
        try {
            // 获取今天的开始和结束时间
            Long todayStart = TimeUtil.getTodayStart();
            Long todayEnd = TimeUtil.getTodayEnd();

            int maxLevel = getCurrentUserQueryLevel();
            BigDecimal amount = operationsMapper.getTotalRechargeAmount(TimeUtil.toLocalDateTime(todayStart), TimeUtil.toLocalDateTime(todayEnd), maxLevel);
            return amount != null ? amount : BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("获取今日充值金额失败，返回默认值0", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 将SubordinateProfit转换为AnchorStatsResponse
     *
     * @param subordinateProfit 下级利润统计数据
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return 主播统计数据响应
     */
    private AnchorStatsResponse convertToAnchorStatsResponse(SubordinateProfit subordinateProfit, Integer startTime, Integer endTime) {
        AnchorStatsResponse stats = new AnchorStatsResponse();

        // 设置基本统计数据
        if (subordinateProfit.getZongyonghu() != null) {
            try {
                stats.setUserCount(Integer.parseInt(subordinateProfit.getZongyonghu()));
            } catch (NumberFormatException e) {
                log.warn("解析总用户数失败：{}", subordinateProfit.getZongyonghu());
                stats.setUserCount(0);
            }
        } else {
            stats.setUserCount(0);
        }

        // 设置总充值金额
        stats.setTotalRecharge(subordinateProfit.getZongchongzhi() != null ?
                subordinateProfit.getZongchongzhi() : BigDecimal.ZERO);

        // 设置实际利润
        stats.setActualProfit(subordinateProfit.getZonglirun() != null ?
                subordinateProfit.getZonglirun() : BigDecimal.ZERO);

        // 设置其他默认值
        stats.setTotalConsume(BigDecimal.ZERO);
        stats.setPeriodNewUserCount(0);
        stats.setPeriodNewInviteCount(0);
        stats.setTotalClaimAmount(BigDecimal.ZERO);
        stats.setTotalShippedAmount(BigDecimal.ZERO);
        stats.setTotalBackpackAmount(BigDecimal.ZERO);
        stats.setPeriodTotalRecharge(stats.getTotalRecharge());
        stats.setTotalTurnover(BigDecimal.ZERO);

        // 计算利润比（实际利润 / 总充值金额 × 100%）
        if (stats.getTotalRecharge() != null && stats.getTotalRecharge().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal profitRatio = stats.getActualProfit()
                    .divide(stats.getTotalRecharge(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            stats.setProfitRatio(profitRatio);
        } else {
            stats.setProfitRatio(BigDecimal.ZERO);
        }

        // 设置时间范围
        stats.setStartTime(startTime);
        stats.setEndTime(endTime);

        return stats;
    }

    /**
     * 获取当前用户的查询层数
     * 基于用户角色编码解析查询权限层数
     *
     * @return 查询层数（1-10之间的整数）
     */
    private int getCurrentUserQueryLevel() {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            if (userId == null) {
                log.debug("用户未登录，使用默认查询层数：{}", RoleCodeUtils.getDefaultQueryLevel());
                return RoleCodeUtils.getDefaultQueryLevel();
            }

            // 检查是否为超级管理员
            if (userId == 1) {
                log.debug("超级管理员用户，使用最大查询层数：{}", RoleCodeUtils.getMaxQueryLevel());
                return RoleCodeUtils.getMaxQueryLevel();
            }

            // 获取用户角色编码
            Set<String> roleCodes = sysUserService.getUserRoles(userId);
            if (roleCodes == null || roleCodes.isEmpty()) {
                log.debug("用户角色为空，用户ID：{}，使用默认查询层数：{}", userId, RoleCodeUtils.getDefaultQueryLevel());
                return RoleCodeUtils.getDefaultQueryLevel();
            }

            // 解析角色编码，获取查询层数
            int queryLevel = RoleCodeUtils.parseQueryLevel(roleCodes);
            log.debug("用户查询层数解析完成，用户ID：{}，角色：{}，查询层数：{}", userId, roleCodes, queryLevel);

            return queryLevel;

        } catch (Exception e) {
            log.warn("获取当前用户查询层数失败，使用默认值：{}，错误：{}", RoleCodeUtils.getDefaultQueryLevel(), e.getMessage());
            return RoleCodeUtils.getDefaultQueryLevel();
        }
    }

    /**
     * 应用数据权限过滤（优化版）
     * 根据当前用户的数据权限范围，获取可访问的手机号列表并设置到查询请求中
     *
     * @param request 查询请求参数
     */
    private void applyDataPermissionFilter(AnchorQueryRequest request) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();
            String dataScope = SecurityUtils.getDataScope();
            boolean isSuperAdmin = SecurityUtils.isSuperAdmin();

            log.info("🔍 权限过滤检查: userId={}, username={}, dataScope={}, isSuperAdmin={}",
                    currentUserId, username, dataScope, isSuperAdmin);

            // 超级管理员或ALL权限用户跳过权限过滤
            if (isSuperAdmin || "ALL".equals(dataScope)) {
                log.info("✅ 超级管理员或ALL权限用户跳过数据权限过滤，允许查看所有数据");
                // 确保清除任何可能存在的权限限制
                request.setAccessiblePhones(null);
                return;
            }

            log.info("🔒 普通用户应用数据权限过滤: userId={}, dataScope={}", currentUserId, dataScope);

            // 根据数据权限范围获取可访问的用户ID列表
            List<Long> accessibleUserIds = deptPermissionService.getAccessibleUserIds(currentUserId, dataScope);
            log.debug("可访问的用户ID数量: {}", accessibleUserIds.size());

            if (accessibleUserIds.isEmpty()) {
                // 如果没有可访问的用户，设置一个不存在的手机号，确保查询结果为空
                request.setAccessiblePhones(List.of("__NO_ACCESS__"));
                log.debug("用户无权限访问任何数据，设置空权限标识");
                return;
            }

            // 批量查询这些用户ID对应的手机号
            List<String> accessiblePhones = getPhonesByUserIds(accessibleUserIds);
            log.debug("可访问的手机号数量: {}", accessiblePhones.size());

            // 设置到查询请求中
            request.setAccessiblePhones(accessiblePhones);

        } catch (Exception e) {
            log.error("应用数据权限过滤失败", e);
            // 出错时采用最严格的权限控制，不允许访问任何数据
            request.setAccessiblePhones(List.of("__NO_ACCESS__"));
        }
    }

    /**
     * 根据用户ID列表查询对应的手机号列表（批量查询优化）
     *
     * @param userIds 用户ID列表
     * @return 手机号列表
     */
    private List<String> getPhonesByUserIds(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return List.of();
        }

        try {
            // 使用MyBatis-Flex数据源切换方式查询主库
            return DataSourceKey.use("master", () -> {
                // 使用批量查询替代N+1查询
                List<SysUser> users = sysUserService.listByIds(userIds);

                List<String> phones = users.stream()
                        .map(SysUser::getPhone)
                        .filter(phone -> phone != null && !phone.trim().isEmpty())
                        .collect(Collectors.toList());

                log.debug("批量查询优化: 从{}个用户ID中提取到{}个有效手机号", userIds.size(), phones.size());
                return phones;
            });
        } catch (Exception e) {
            log.error("根据用户ID批量查询手机号失败", e);
            return List.of();
        }
    }

    /**
     * 创建空的下级利润统计数据
     * @return 空的统计数据对象
     */
    private SubordinateProfit createEmptySubordinateProfit() {
        SubordinateProfit result = new SubordinateProfit();
        result.setZongyonghu("0");
        result.setZongchongzhi(BigDecimal.ZERO);
        result.setZonglirun(BigDecimal.ZERO);
        return result;
    }

    @Override
    public String getUserFullPhone(Long userId) {
        try {
            log.info("开始获取用户完整手机号: userId={}", userId);

            // 参数验证
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID不能为空且必须大于0");
            }

            // 权限验证：检查当前用户是否有权限查看完整手机号
            Long currentUserId = SecurityUtils.getUserId();
            String dataScope = SecurityUtils.getDataScope();
            boolean isSuperAdmin = SecurityUtils.isSuperAdmin();

            log.debug("权限检查: currentUserId={}, dataScope={}, isSuperAdmin={}",
                    currentUserId, dataScope, isSuperAdmin);

            // 超级管理员可以查看所有用户的完整手机号
            if (!isSuperAdmin && !"ALL".equals(dataScope)) {
                // 普通用户需要检查数据权限
                List<Long> accessibleUserIds = deptPermissionService.getAccessibleUserIds(currentUserId, dataScope);
                if (!accessibleUserIds.contains(userId)) {
                    throw new SecurityException("无权限查看该用户的完整手机号");
                }
            }

            // 使用MyBatis-Flex数据源切换方式查询主库
            return DataSourceKey.use("master", () -> {
                SysUser user = sysUserService.getById(userId);
                if (user == null) {
                    throw new IllegalArgumentException("用户不存在: userId=" + userId);
                }

                String fullPhone = user.getPhone();
                if (fullPhone == null || fullPhone.trim().isEmpty()) {
                    throw new IllegalArgumentException("用户手机号为空");
                }

                log.info("获取用户完整手机号成功: userId={}", userId);
                return fullPhone;
            });

        } catch (Exception e) {
            log.error("获取用户完整手机号失败: userId={}", userId, e);
            throw e;
        }
    }

    /**
     * 根据sys_user的ID获取对应的vim_user的ID
     * 通过手机号进行关联查询
     *
     * @param sysUserId sys_user表的用户ID
     * @return vim_user表的用户ID，如果未找到则返回null
     */
    private Integer getVimUserIdBySysUserId(Long sysUserId) {
        try {
            log.debug("开始根据sys_user ID获取vim_user ID: sysUserId={}", sysUserId);

            // 先从主库查询sys_user的手机号
            String phone = DataSourceKey.use("master", () -> {
                SysUser sysUser = sysUserService.getById(sysUserId);
                if (sysUser == null) {
                    log.warn("未找到sys_user记录: sysUserId={}", sysUserId);
                    return null;
                }
                return sysUser.getPhone();
            });

            if (phone == null || phone.trim().isEmpty()) {
                log.warn("sys_user的手机号为空: sysUserId={}", sysUserId);
                return null;
            }

            // 再从从库查询vim_user
            Integer vimUserId = DataSourceKey.use("slave", () -> {
                return operationsMapper.getVimUserIdByPhone(phone);
            });

            if (vimUserId != null) {
                log.debug("成功找到对应的vim_user ID: sysUserId={}, phone={}, vimUserId={}",
                        sysUserId, DataMaskingUtils.maskPhone(phone), vimUserId);
            } else {
                log.warn("未找到对应的vim_user记录: sysUserId={}, phone={}",
                        sysUserId, DataMaskingUtils.maskPhone(phone));
            }

            return vimUserId;

        } catch (Exception e) {
            log.error("根据sys_user ID获取vim_user ID失败: sysUserId={}", sysUserId, e);
            return null;
        }
    }
}
