package com.jcloud.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.*;
import com.jcloud.admin.vo.SysUserDetailVO;
import com.jcloud.admin.vo.SysUserVO;
import com.jcloud.common.constant.CommonConstants;
import com.mybatisflex.core.datasource.DataSourceKey;
import com.jcloud.common.dto.*;
import com.jcloud.common.entity.*;
import com.jcloud.common.enums.BatchTransactionMode;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.*;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.DataMaskingUtils;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.util.TimeUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


// 表定义将由MyBatis-Flex自动生成

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    private final SysUserRoleMapper userRoleMapper;
    private final SysUserDeptMapper userDeptMapper;
    private final MenuPermissionService menuPermissionService;
    private final BCryptPasswordEncoder passwordEncoder;
    private final DeptPermissionService deptPermissionService;
    private final VimUserMapper vimUserMapper;
    private final CacheManager cacheManager;
    private final SysRoleService roleService;
    private final SysDeptService deptService;
    private final SysDeptMapper deptMapper;



    public SysUserServiceImpl(SysUserRoleMapper userRoleMapper,
                              SysUserDeptMapper userDeptMapper,
                              MenuPermissionService menuPermissionService,
                              BCryptPasswordEncoder passwordEncoder,
                              DeptPermissionService deptPermissionService,
                              VimUserMapper vimUserMapper,
                              CacheManager cacheManager,
                              SysRoleService roleService,
                              SysDeptService deptService,
                              SysDeptMapper deptMapper) {
        this.userRoleMapper = userRoleMapper;
        this.userDeptMapper = userDeptMapper;
        this.menuPermissionService = menuPermissionService;
        this.passwordEncoder = passwordEncoder;
        this.deptPermissionService = deptPermissionService;
        this.vimUserMapper = vimUserMapper;
        this.cacheManager = cacheManager;
        this.roleService = roleService;
        this.deptService = deptService;
        this.deptMapper = deptMapper;
    }

    /**
     * 应用启动时清除可能存在的旧格式缓存
     */
    @PostConstruct
    public void clearOldFormatCache() {
        try {
            if (cacheManager.getCache("userCache") != null) {
                cacheManager.getCache("userCache").clear();
                log.info("已清除用户缓存，解决序列化格式兼容性问题");
            }
        } catch (Exception e) {
            log.warn("清除用户缓存失败，但不影响系统运行", e);
        }
    }

    @Override
    public PageResult<SysUser> pageUsers(UserQueryRequest queryRequest) {
        QueryWrapper queryWrapper = getQueryWrapper();
        // 构建查询条件
        if (StrUtil.isNotBlank(queryRequest.getUsername())) {
            queryWrapper.like("username", queryRequest.getUsername());
        }
        if (StrUtil.isNotBlank(queryRequest.getNickname())) {
            queryWrapper.like("nickname", queryRequest.getNickname());
        }
        if (StrUtil.isNotBlank(queryRequest.getRealName())) {
            queryWrapper.like("real_name", queryRequest.getRealName());
        }
        if (StrUtil.isNotBlank(queryRequest.getEmail())) {
            queryWrapper.like("email", queryRequest.getEmail());
        }
        if (StrUtil.isNotBlank(queryRequest.getPhone())) {
            queryWrapper.like("phone", queryRequest.getPhone());
        }
        if (queryRequest.getGender() != null) {
            queryWrapper.eq("gender", queryRequest.getGender());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq("status", queryRequest.getStatus());
        }
        if (queryRequest.getDeptId() != null) {
            // TODO: 通过用户部门关联表查询，暂时跳过
        }
        if (queryRequest.getIsAdmin() != null) {
            queryWrapper.eq("is_admin", queryRequest.getIsAdmin());
        }

        // 角色筛选 - 通过用户角色关联表查询
        if (queryRequest.getRoleId() != null || StrUtil.isNotBlank(queryRequest.getRoleCode())) {
            Long tenantId = SecurityUtils.getTenantId();
            List<Long> userIdsWithRole = getUserIdsByRole(queryRequest.getRoleId(), queryRequest.getRoleCode(), tenantId);

            if (userIdsWithRole.isEmpty()) {
                // 如果没有用户拥有指定角色，返回空结果
                queryWrapper.eq("id", -1); // 使用不存在的ID确保返回空结果
            } else {
                queryWrapper.in("id", userIdsWithRole);
            }
        }

        if (queryRequest.getCreateTimeStart() != null) {
            queryWrapper.ge("create_time", queryRequest.getCreateTimeStart());
        }
        if (queryRequest.getCreateTimeEnd() != null) {
            queryWrapper.le("create_time", queryRequest.getCreateTimeEnd());
        }

        // 添加排序
        if (StrUtil.isNotBlank(queryRequest.getOrderBy())) {
            queryWrapper.orderBy(queryRequest.getOrderBy(), CommonConstants.ORDER_ASC.equalsIgnoreCase(queryRequest.getOrderDirection()));
        } else {
            queryWrapper.orderBy("create_time", false);
        }

        Page<SysUser> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
        Page<SysUser> result = baseMapper.paginate(page, queryWrapper);

        return PageResult.of(result.getRecords(), result.getTotalRow(),
                queryRequest.getPageNum(), queryRequest.getPageSize());
    }

    /**
     * 根据角色ID或角色编码获取用户ID列表
     *
     * @param roleId 角色ID
     * @param roleCode 角色编码
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    private List<Long> getUserIdsByRole(Long roleId, String roleCode, Long tenantId) {
        try {
            if (roleId != null) {
                // 通过角色ID查询
                return userRoleMapper.selectUserIdsByRoleId(roleId, tenantId);
            } else if (StrUtil.isNotBlank(roleCode)) {
                // 通过角色编码查询
                return userRoleMapper.selectUserIdsByRoleCode(roleCode.trim(), tenantId);
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("根据角色查询用户ID列表失败: roleId={}, roleCode={}", roleId, roleCode, e);
            return Collections.emptyList();
        }
    }

    @Override
    public PageResult<SysUserVO> pageUsersForDisplay(UserQueryRequest queryRequest) {
        long startTime = System.currentTimeMillis();
        try {
            // 1. 查询用户分页数据
            PageResult<SysUser> userPageResult = pageUsers(queryRequest);
            List<SysUser> users = userPageResult.getRecords();

            if (users.isEmpty()) {
                log.info("✅ 用户列表为空，直接返回");
                return PageResult.of(Collections.emptyList(), 0L,
                        queryRequest.getPageNum(), queryRequest.getPageSize());
            }

            // 2. 批量查询优化 - 一次性获取所有关联数据
            List<Long> userIds = users.stream().map(SysUser::getId).collect(Collectors.toList());
            Long tenantId = SecurityUtils.getTenantId();

            log.info("📊 开始批量查询关联数据，用户数量: {}, 租户ID: {}", userIds.size(), tenantId);

            // 3. 批量查询用户角色（优化版 - 减少N+1查询）
            Map<Long, List<SysRole>> userRolesMap = batchGetUserRoles(userIds, tenantId);

            // 4. 批量查询用户部门（复用现有方法）
            Map<Long, Long> userPrimaryDeptMap = batchGetUserPrimaryDepts(userIds, tenantId);
            Map<Long, List<Long>> userAllDeptsMap = batchGetUserAllDepts(userIds, tenantId);

            // 5. 批量查询部门信息（复用现有的listByIds方法）
            Set<Long> allDeptIds = userPrimaryDeptMap.values().stream()
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            // 添加所有部门的部门ID
            userAllDeptsMap.values().forEach(allDeptIds::addAll);
            Map<Long, SysDept> deptMap = batchGetDepts(allDeptIds);

            log.info("📈 批量查询完成 - 角色关联: {}, 主部门: {}, 所有部门: {}, 部门详情: {}",
                    userRolesMap.size(), userPrimaryDeptMap.size(), userAllDeptsMap.size(), deptMap.size());

            // 6. 转换为VO（复用现有的转换逻辑）
            List<SysUserVO> maskedUsers = users.stream()
                    .map(user -> convertToMaskedUserVOOptimized(user, userRolesMap,
                            userPrimaryDeptMap, userAllDeptsMap, deptMap))
                    .collect(Collectors.toList());

            long endTime = System.currentTimeMillis();
            log.info("🎯 用户分页查询优化完成，总耗时: {}ms，平均每用户: {}ms",
                    endTime - startTime, (endTime - startTime) / users.size());

            return PageResult.of(maskedUsers, userPageResult.getTotal(),
                    userPageResult.getPageNum(), userPageResult.getPageSize());

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("❌ 用户分页查询优化失败，耗时: {}ms", endTime - startTime, e);
            throw e;
        }
    }


    /**
     * 将SysUser转换为脱敏的SysUserVO
     *
     * @param user 原始用户对象
     * @return 脱敏的用户VO
     */
    private SysUserVO convertToMaskedUserVO(SysUser user) {
        SysUserVO userVO = new SysUserVO();
        BeanUtil.copyProperties(user, userVO);

        // 手动处理时间戳转换（BaseEntity中是Long秒级时间戳，SysUserVO中是LocalDateTime）
        if (user.getCreateTime() != null) {
            userVO.setCreateTime(TimeUtil.toLocalDateTime(user.getCreateTime()));
        }
        if (user.getUpdateTime() != null) {
            userVO.setUpdateTime(TimeUtil.toLocalDateTime(user.getUpdateTime()));
        }
        if (user.getLastLoginTime() != null) {
            userVO.setLastLoginTime(TimeUtil.toLocalDateTime(user.getLastLoginTime()));
        }
        if (user.getPasswordUpdateTime() != null) {
            userVO.setPasswordUpdateTime(TimeUtil.toLocalDateTime(user.getPasswordUpdateTime()));
        }

        // 手动处理脱敏
        if (StrUtil.isNotBlank(user.getPhone())) {
            userVO.setPhone(DataMaskingUtils.maskPhone(user.getPhone()));
        }

        if (StrUtil.isNotBlank(user.getEmail())) {
            userVO.setEmail(DataMaskingUtils.maskEmail(user.getEmail()));
        }

        try {
            // 获取用户角色信息
            Long tenantId = SecurityUtils.getTenantId();
            List<SysRole> roles = userRoleMapper.selectRolesByUserId(user.getId(), tenantId);

            if (roles != null && !roles.isEmpty()) {
                List<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toList());
                List<String> roleNames = roles.stream().map(SysRole::getRoleName).collect(Collectors.toList());

                userVO.setRoleIds(roleIds);
                userVO.setRoleNames(roleNames);
            }

            // 获取用户主部门信息
            Long primaryDeptId = deptPermissionService.getUserPrimaryDeptId(user.getId());
            if (primaryDeptId != null) {
                userVO.setDeptId(primaryDeptId);
                // 获取部门名称
                SysDept dept = deptService.getById(primaryDeptId);
                if (dept != null) {
                    userVO.setDeptName(dept.getDeptName());
                }
            }

            // 获取用户所有部门
            List<Long> allDeptIds = deptPermissionService.getUserDeptIds(user.getId());
            userVO.setDeptIds(allDeptIds);

        } catch (Exception e) {
            log.warn("获取用户角色或部门信息失败: userId={}", user.getId(), e);
            // 即使获取角色部门信息失败，也返回基本用户信息
        }

        return userVO;
    }

    /**
     * 批量查询用户角色信息（优化版 - 真正的批量查询）
     *
     * @param userIds  用户ID列表
     * @param tenantId 租户ID
     * @return 用户角色映射
     */
    private Map<Long, List<SysRole>> batchGetUserRoles(List<Long> userIds, Long tenantId) {
        if (userIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            long startTime = System.currentTimeMillis();

            // 使用真正的批量查询 - 一次SQL查询所有用户的角色
            List<SysUserRoleMapper.UserRoleResult> results = userRoleMapper.selectRolesByUserIds(userIds, tenantId);

            // 将结果按用户ID分组
            Map<Long, List<SysRole>> userRolesMap = new HashMap<>();
            for (SysUserRoleMapper.UserRoleResult result : results) {
                userRolesMap.computeIfAbsent(result.getUserId(), k -> new ArrayList<>())
                        .add(convertToSysRole(result));
            }

            long endTime = System.currentTimeMillis();

            log.info("🚀 批量查询用户角色完成（优化版），查询{}个用户，返回{}个用户的角色记录，耗时: {}ms",
                    userIds.size(), userRolesMap.size(), endTime - startTime);

            return userRolesMap;
        } catch (Exception e) {
            log.error("批量查询用户角色失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 将UserRoleResult转换为SysRole（复用BeanUtil工具类）
     */
    private SysRole convertToSysRole(SysUserRoleMapper.UserRoleResult result) {
        SysRole role = new SysRole();
        // 复用现有的BeanUtil工具类进行属性复制
        BeanUtil.copyProperties(result, role);
        return role;
    }

    /**
     * 批量查询用户主部门
     *
     * @param userIds  用户ID列表
     * @param tenantId 租户ID
     * @return 用户主部门映射
     */
    private Map<Long, Long> batchGetUserPrimaryDepts(List<Long> userIds, Long tenantId) {
        if (userIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            long startTime = System.currentTimeMillis();
            List<SysUserDept> results = userDeptMapper.selectPrimaryDeptsByUserIds(userIds, tenantId);
            long endTime = System.currentTimeMillis();

            log.info("📊 批量查询用户主部门完成，查询{}个用户，返回{}条部门记录，耗时: {}ms",
                    userIds.size(), results.size(), endTime - startTime);

            return results.stream().collect(Collectors.toMap(
                    SysUserDept::getUserId, SysUserDept::getDeptId
            ));
        } catch (Exception e) {
            log.error("批量查询用户主部门失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询用户所有部门（优化版 - 真正的批量查询）
     *
     * @param userIds  用户ID列表
     * @param tenantId 租户ID
     * @return 用户所有部门映射
     */
    private Map<Long, List<Long>> batchGetUserAllDepts(List<Long> userIds, Long tenantId) {
        if (userIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            long startTime = System.currentTimeMillis();

            // 使用真正的批量查询 - 一次SQL查询所有用户的部门关联
            List<SysUserDept> results = userDeptMapper.selectAllDeptsByUserIds(userIds, tenantId);

            // 将结果按用户ID分组
            Map<Long, List<Long>> userAllDeptsMap = new HashMap<>();
            for (SysUserDept userDept : results) {
                userAllDeptsMap.computeIfAbsent(userDept.getUserId(), k -> new ArrayList<>())
                        .add(userDept.getDeptId());
            }

            long endTime = System.currentTimeMillis();

            log.info("🚀 批量查询用户所有部门完成（优化版），查询{}个用户，返回{}个用户的部门记录，耗时: {}ms",
                    userIds.size(), userAllDeptsMap.size(), endTime - startTime);

            return userAllDeptsMap;
        } catch (Exception e) {
            log.error("批量查询用户所有部门失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询部门信息（使用新增的真正批量查询方法）
     *
     * @param deptIds 部门ID集合
     * @return 部门信息映射
     */
    private Map<Long, SysDept> batchGetDepts(Set<Long> deptIds) {
        if (deptIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            long startTime = System.currentTimeMillis();

            //一次SQL查询指定的部门
            List<Long> deptIdList = new ArrayList<>(deptIds);
            Long tenantId = SecurityUtils.getTenantId();
            List<SysDept> depts = deptMapper.selectByDeptIds(deptIdList, tenantId);

            // 转换为Map
            Map<Long, SysDept> deptMap = depts.stream()
                    .collect(Collectors.toMap(SysDept::getId, Function.identity()));

            long endTime = System.currentTimeMillis();
            log.debug("批量查询部门信息完成（真正的批量查询），查询{}个部门，返回{}条记录，耗时: {}ms",
                    deptIds.size(), deptMap.size(), endTime - startTime);

            return deptMap;
        } catch (Exception e) {
            log.error("批量查询部门信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 优化版的用户VO转换方法（使用预查询的数据）
     *
     * @param user               原始用户对象
     * @param userRolesMap       用户角色映射
     * @param userPrimaryDeptMap 用户主部门映射
     * @param userAllDeptsMap    用户所有部门映射
     * @param deptMap            部门信息映射
     * @return 脱敏的用户VO
     */
    private SysUserVO convertToMaskedUserVOOptimized(SysUser user,
                                                     Map<Long, List<SysRole>> userRolesMap,
                                                     Map<Long, Long> userPrimaryDeptMap,
                                                     Map<Long, List<Long>> userAllDeptsMap,
                                                     Map<Long, SysDept> deptMap) {
        SysUserVO userVO = new SysUserVO();
        BeanUtil.copyProperties(user, userVO);

        // 手动处理时间戳转换（BaseEntity中是Long秒级时间戳，SysUserVO中是LocalDateTime）
        if (user.getCreateTime() != null) {
            userVO.setCreateTime(TimeUtil.toLocalDateTime(user.getCreateTime()));
        }
        if (user.getUpdateTime() != null) {
            userVO.setUpdateTime(TimeUtil.toLocalDateTime(user.getUpdateTime()));
        }
        if (user.getLastLoginTime() != null) {
            userVO.setLastLoginTime(TimeUtil.toLocalDateTime(user.getLastLoginTime()));
        }
        if (user.getPasswordUpdateTime() != null) {
            userVO.setPasswordUpdateTime(TimeUtil.toLocalDateTime(user.getPasswordUpdateTime()));
        }

        // 手动处理脱敏
        if (StrUtil.isNotBlank(user.getPhone())) {
            userVO.setPhone(DataMaskingUtils.maskPhone(user.getPhone()));
        }

        if (StrUtil.isNotBlank(user.getEmail())) {
            userVO.setEmail(DataMaskingUtils.maskEmail(user.getEmail()));
        }

        try {
            // 从预查询的数据中获取用户角色信息
            List<SysRole> roles = userRolesMap.get(user.getId());
            if (roles != null && !roles.isEmpty()) {
                List<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toList());
                List<String> roleNames = roles.stream().map(SysRole::getRoleName).collect(Collectors.toList());

                userVO.setRoleIds(roleIds);
                userVO.setRoleNames(roleNames);
            }

            // 从预查询的数据中获取用户主部门信息
            Long primaryDeptId = userPrimaryDeptMap.get(user.getId());
            if (primaryDeptId != null) {
                userVO.setDeptId(primaryDeptId);
                // 从预查询的部门信息中获取部门名称
                SysDept dept = deptMap.get(primaryDeptId);
                if (dept != null) {
                    userVO.setDeptName(dept.getDeptName());
                }
            }

            // 从预查询的数据中获取用户所有部门
            List<Long> allDeptIds = userAllDeptsMap.get(user.getId());
            if (allDeptIds != null) {
                userVO.setDeptIds(allDeptIds);
            } else {
                userVO.setDeptIds(new ArrayList<>());
            }

        } catch (Exception e) {
            log.warn("转换用户VO时发生错误: userId={}", user.getId(), e);
            // 即使获取角色部门信息失败，也返回基本用户信息
        }

        return userVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(UserCreateRequest createRequest) {
        // 1. 验证用户名唯一性
        if (isUsernameExists(createRequest.getUsername(), null)) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "用户名已存在");
        }

        // 2. 验证邮箱唯一性
        if (StrUtil.isNotBlank(createRequest.getEmail()) &&
                isEmailExists(createRequest.getEmail(), null)) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "邮箱已存在");
        }

        // 3. 验证手机号唯一性
        if (StrUtil.isNotBlank(createRequest.getPhone()) &&
                isPhoneExists(createRequest.getPhone(), null)) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "手机号已存在");
        }

        // 4. 创建用户实体
        SysUser user = new SysUser();
        user.setUsername(createRequest.getUsername());
        user.setPassword(passwordEncoder.encode(createRequest.getPassword()));
        user.setNickname(createRequest.getNickname());
        user.setRealName(createRequest.getRealName());
        user.setEmail(createRequest.getEmail());
        user.setPhone(createRequest.getPhone());
        user.setGender(createRequest.getGender());
        user.setBirthday(createRequest.getBirthday());
        user.setStatus(createRequest.getStatus());
        user.setIsAdmin(CommonConstants.STATUS_DISABLED); // 默认非管理员
        user.setRemark(createRequest.getRemark());
        user.setPasswordUpdateTime(TimeUtil.now());

        // 5. 保存用户
        boolean success = save(user);
        if (!success) {
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR, "创建用户失败");
        }

        // 6. 分配角色
        if (createRequest.getRoleIds() != null && !createRequest.getRoleIds().isEmpty()) {
            assignRoles(user.getId(), createRequest.getRoleIds());
        }

        log.info("创建用户成功: {}", user.getUsername());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserUpdateRequest updateRequest) {
        // 1. 验证用户是否存在
        SysUser existUser = getById(updateRequest.getId());
        if (existUser == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND, "用户不存在");
        }

        // 2. 验证邮箱唯一性
        if (StrUtil.isNotBlank(updateRequest.getEmail()) &&
                isEmailExists(updateRequest.getEmail(), updateRequest.getId())) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "邮箱已存在");
        }

        // 3. 验证手机号唯一性
        if (StrUtil.isNotBlank(updateRequest.getPhone()) &&
                isPhoneExists(updateRequest.getPhone(), updateRequest.getId())) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "手机号已存在");
        }

        // 4. 更新用户信息
        SysUser user = new SysUser();
        user.setId(updateRequest.getId());
        user.setNickname(updateRequest.getNickname());
        user.setRealName(updateRequest.getRealName());
        user.setEmail(updateRequest.getEmail());
        user.setPhone(updateRequest.getPhone());
        user.setGender(updateRequest.getGender());
        user.setBirthday(updateRequest.getBirthday());
        user.setStatus(updateRequest.getStatus());
        user.setRemark(updateRequest.getRemark());
        boolean success = updateById(user);
        if (!success) {
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR, "更新用户失败");
        }

        // 5. 更新角色分配
        if (updateRequest.getRoleIds() != null) {
            assignRoles(updateRequest.getId(), updateRequest.getRoleIds());
        }

        log.info("更新用户成功: {}", existUser.getUsername());
        return true;
    }

    @Override
    @Cacheable(value = "userCache", key = "'username:' + #username + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public SysUser getUserByUsername(String username) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("查询用户: username={}, tenantId={}", username, tenantId);

        try {
            SysUser user = baseMapper.selectByUsername(username, tenantId);
            log.debug("查询结果: {}", user != null ? "找到用户" : "未找到用户");
            return user;
        } catch (Exception e) {
            log.error("查询用户失败: username={}, tenantId={}", username, tenantId, e);
            // 如果是类型转换错误，清理缓存后重试
            if (e.getCause() instanceof ClassCastException) {
                log.warn("检测到缓存类型转换错误，清理用户缓存后重试");
                clearUserCache(username, tenantId);
                return baseMapper.selectByUsername(username, tenantId);
            }
            throw e;
        }
    }

    /**
     * 清理指定用户的缓存
     */
    private void clearUserCache(String username, Long tenantId) {
        try {
            String cacheKey = "username:" + username + ":tenant:" + tenantId;
            if (cacheManager.getCache("userCache") != null) {
                cacheManager.getCache("userCache").evict(cacheKey);
                log.info("已清理用户缓存: {}", cacheKey);
            }
        } catch (Exception e) {
            log.warn("清理用户缓存失败", e);
        }
    }

    @Override
    public void clearUserCacheByUsername(String username) {
        Long tenantId = SecurityUtils.getTenantId();
        clearUserCache(username, tenantId);
        log.info("已清理用户缓存: username={}, tenantId={}", username, tenantId);
    }

    @Override
    public SysUser getUserByUsernameFromDatabase(String username) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("直接从数据库查询用户: username={}, tenantId={}", username, tenantId);

        try {
            SysUser user = baseMapper.selectByUsername(username, tenantId);
            log.debug("数据库查询结果: {}", user != null ? "找到用户" : "未找到用户");
            if (user != null && user.getPassword() != null) {
                log.debug("用户密码字段正常: username={}, passwordLength={}", username, user.getPassword().length());
            } else if (user != null) {
                log.warn("用户密码字段为空: username={}", username);
            }
            return user;
        } catch (Exception e) {
            log.error("从数据库查询用户失败: username={}, tenantId={}", username, tenantId, e);
            throw e;
        }
    }

    // MyBatis-Flex自动处理数据权限，无需额外的权限检查方法

    @Override
    // @Cacheable(value = "userCache", key = "'email:' + #email + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public SysUser getUserByEmail(String email) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.selectByEmail(email, tenantId);
    }

    @Override
    @Cacheable(value = "userCache", key = "'phone:' + #phone + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public SysUser getUserByPhone(String phone) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.selectByPhone(phone, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long userId, String newPassword) {
        // 验证用户是否存在
        SysUser user = getById(userId);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND, "用户不存在");
        }

        String encodedPassword = passwordEncoder.encode(newPassword);
        Long tenantId = SecurityUtils.getTenantId();

        int result = baseMapper.resetPassword(userId, encodedPassword, tenantId);
        if (result > 0) {
            log.info("重置用户密码成功: {}", user.getUsername());
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // 验证用户是否存在
        SysUser user = getById(userId);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND, "用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw BusinessException.of(ResultCode.PASSWORD_ERROR, "原密码错误");
        }

        // 更新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        Long tenantId = SecurityUtils.getTenantId();

        int result = baseMapper.resetPassword(userId, encodedPassword, tenantId);
        if (result > 0) {
            log.info("修改用户密码成功: {}", user.getUsername());
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatus(Long userId, Integer status) {
        SysUser user = new SysUser();
        user.setId(userId);
        user.setStatus(status);

        boolean success = updateById(user);
        if (success) {
            log.info("更新用户状态成功: userId={}, status={}", userId, status);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatusBatch(List<Long> userIds, Integer status) {
        Long tenantId = SecurityUtils.getTenantId();
        int result = baseMapper.updateStatusBatch(userIds, status, tenantId);

        if (result > 0) {
            log.info("批量更新用户状态成功: userIds={}, status={}", userIds, status);
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "userRoles", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()"),
            @CacheEvict(value = "userPermissions", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    })
    public boolean assignRoles(Long userId, List<Long> roleIds) {
        Long tenantId = SecurityUtils.getTenantId();
        Long currentUserId = SecurityUtils.getUserId();

        // 1. 删除原有角色关联
        userRoleMapper.deleteByUserId(userId, tenantId);

        // 2. 添加新的角色关联
        if (roleIds != null && !roleIds.isEmpty()) {
            for (Long roleId : roleIds) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRole.setTenantId(tenantId);
                userRole.setCreateBy(currentUserId);
                userRoleMapper.insert(userRole);
            }
            log.info("分配用户角色成功: userId={}, roleIds={}", userId, roleIds);
        }
        // 如果roleIds为空，表示清空角色，也算成功

        // 3. 清理相关缓存
        clearUserRelatedCaches(userId, tenantId);

        return true;
    }

    @Override
    public List<Long> getUserRoleIds(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        return userRoleMapper.selectRolesByUserId(userId, tenantId)
                .stream()
                .map(SysRole::getId)
                .toList();
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeUserId) {
        Long tenantId = SecurityUtils.getTenantId();
        SysUser user = baseMapper.selectByUsername(username, tenantId);

        if (user == null) {
            return false;
        }

        // 如果是更新操作，排除当前用户
        return !user.getId().equals(excludeUserId);
    }

    @Override
    public boolean isEmailExists(String email, Long excludeUserId) {
        if (StrUtil.isBlank(email)) {
            return false;
        }

        Long tenantId = SecurityUtils.getTenantId();
        SysUser user = baseMapper.selectByEmail(email, tenantId);

        if (user == null) {
            return false;
        }

        // 如果是更新操作，排除当前用户
        return !user.getId().equals(excludeUserId);
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeUserId) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }

        Long tenantId = SecurityUtils.getTenantId();
        SysUser user = baseMapper.selectByPhone(phone, tenantId);

        if (user == null) {
            return false;
        }

        // 如果是更新操作，排除当前用户
        return !user.getId().equals(excludeUserId);
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Set<String> getUserPermissions(Long userId) {
        log.debug("SysUserService.getUserPermissions() 被调用: userId={}", userId);

        if (userId == null) {
            return new HashSet<>();
        }

        // 优化：避免重复查询，直接使用缓存的用户信息
        SysUser user = getById(userId);
        if (user == null) {
            log.warn("用户不存在: userId={}", userId);
            return new HashSet<>();
        }

        return getUserPermissions(user);
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#user.id + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Set<String> getUserPermissions(SysUser user) {
        log.info("🚀 SysUserService.getUserPermissions(SysUser) 被调用: userId={}", user != null ? user.getId() : null);
        log.info(" 用户信息检查: user={}", user);

        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            // 超级管理员拥有所有权限
            Set<String> allPermissions = new HashSet<>();
            allPermissions.add("*:*:*");
            log.info(" 超级管理员权限返回: permissions={}", allPermissions);
            return allPermissions;
        }

        // 使用新的菜单权限服务获取用户权限
        List<String> permissions = menuPermissionService.getUserPermissionCodes(user.getId());
        Set<String> permissionSet = new HashSet<>(permissions);
        log.info(" 普通用户权限返回: permissions={}", permissionSet);
        return permissionSet;
    }

    @Override
    @Cacheable(value = "userRoles", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Set<String> getUserRoles(Long userId) {
        if (userId == null) {
            return new HashSet<>();
        }

        // 优化：避免重复查询，直接使用缓存的用户信息
        SysUser user = getById(userId);
        if (user == null) {
            log.warn("用户不存在: userId={}", userId);
            return new HashSet<>();
        }

        return getUserRoles(user);
    }

    @Override
    @Cacheable(value = "userRoles", key = "#user.id + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Set<String> getUserRoles(SysUser user) {
        Long tenantId = SecurityUtils.getTenantId();

        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            // 超级管理员拥有admin角色
            Set<String> adminRoles = new HashSet<>();
            adminRoles.add(CommonConstants.SUPER_ADMIN_ROLE);
            return adminRoles;
        }

        // 获取用户角色
        List<String> roles = userRoleMapper.selectRoleCodesByUserId(user.getId(), tenantId);
        return new HashSet<>(roles);
    }

    @Override
    public SysUserDetailVO getUserDetailById(Long userId) {
        if (userId == null) {
            return null;
        }

        // 获取用户基本信息
        SysUser user = getById(userId);
        if (user == null) {
            return null;
        }

        // 创建用户详情VO
        SysUserDetailVO userDetail = new SysUserDetailVO();
        // 复制基本属性
        userDetail.setId(user.getId());
        userDetail.setUsername(user.getUsername());
        userDetail.setNickname(user.getNickname());
        userDetail.setRealName(user.getRealName());
        userDetail.setAvatar(user.getAvatar());
        // 对敏感信息进行脱敏处理
        userDetail.setEmail(StrUtil.isNotBlank(user.getEmail()) ? DataMaskingUtils.maskEmail(user.getEmail()) : user.getEmail());
        userDetail.setPhone(StrUtil.isNotBlank(user.getPhone()) ? DataMaskingUtils.maskPhone(user.getPhone()) : user.getPhone());
        userDetail.setGender(user.getGender());
        userDetail.setBirthday(user.getBirthday());
        userDetail.setStatus(user.getStatus());
        userDetail.setIsAdmin(user.getIsAdmin());
        userDetail.setLastLoginTime(user.getLastLoginTime());
        userDetail.setLastLoginIp(user.getLastLoginIp());
        userDetail.setPasswordUpdateTime(user.getPasswordUpdateTime());
        userDetail.setRemark(user.getRemark());
        userDetail.setCreateTime(user.getCreateTime());
        userDetail.setUpdateTime(user.getUpdateTime());
        userDetail.setCreateBy(user.getCreateBy());
        userDetail.setUpdateBy(user.getUpdateBy());
        userDetail.setDeleted(user.getDeleted());
        userDetail.setTenantId(user.getTenantId());
        userDetail.setVersion(user.getVersion());
        try {
            // 获取用户主部门信息
            Long primaryDeptId = deptPermissionService.getUserPrimaryDeptId(userId);
            if (primaryDeptId != null) {
                userDetail.setDeptId(primaryDeptId);
                // 这里可以进一步查询部门名称
                // SysDept dept = deptService.getById(primaryDeptId);
                // if (dept != null) {
                //     userDetail.setDeptName(dept.getDeptName());
                // }
            }

            // 获取用户所有部门
            List<Long> allDeptIds = deptPermissionService.getUserDeptIds(userId);
            userDetail.setDeptIds(allDeptIds);

            // 获取用户角色
            Long tenantId = SecurityUtils.getTenantId();
            List<String> roleCodes = userRoleMapper.selectRoleCodesByUserId(userId, tenantId);
            // 这里可以进一步查询角色ID和名称
            // userDetail.setRoleNames(roleNames);

            // 获取用户权限
            List<String> permissions = menuPermissionService.getUserPermissionCodes(userId);
            userDetail.setPermissions(permissions);

        } catch (Exception e) {
            log.error("获取用户详情时发生错误: userId={}", userId, e);
            // 即使获取部门角色信息失败，也返回基本用户信息
        }

        return userDetail;
    }

    @Override
    public SysUserDetailVO getUserDetailForEdit(Long userId) {
        // 复用现有的getUserDetailById方法获取完整信息
        SysUserDetailVO userDetail = getUserDetailById(userId);
        if (userDetail == null) {
            return null;
        }

        // 获取原始用户数据（不脱敏）
        SysUser user = getById(userId);
        if (user != null) {
            // 恢复原始的敏感信息（用于编辑）
            userDetail.setEmail(user.getEmail()); // 原始邮箱
            userDetail.setPhone(user.getPhone()); // 原始手机号
        }

        return userDetail;
    }




    /**
     * 获取默认租户ID
     */
    private Long getDefaultTenantId() {
        try {
            Long tenantId = SecurityUtils.getTenantId();
            return tenantId != null ? tenantId : 1L;
        } catch (Exception e) {
            log.warn("无法获取当前租户ID，使用默认租户ID: 1");
            return 1L;
        }
    }



    /**
     * 批量查询现有用户（根据用户名）
     */
    private Map<String, SysUser> batchQueryExistingUsersByUsername(List<String> usernames, Long tenantId) {
        if (usernames.isEmpty()) {
            return Map.of();
        }

        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where("username IN (?)", usernames)
                    .and("tenant_id = ?", tenantId)
                    .and("deleted = ?", 0);

            List<SysUser> existingUsers = baseMapper.selectListByQuery(queryWrapper);
            return existingUsers.stream()
                    .collect(Collectors.toMap(SysUser::getUsername, Function.identity()));
        } catch (Exception e) {
            log.error("批量查询用户名失败", e);
            return Map.of();
        }
    }

    /**
     * 批量保存用户并分配角色（新版本 - 支持详细结果返回）
     *
     * <p><strong>推荐使用严格模式（STRICT）</strong>，基于性能分析：</p>
     * <ul>
     *   <li>严格模式：2次数据库写入操作，性能最佳</li>
     *   <li>宽松模式：N×(2+M)次数据库写入操作，性能较低</li>
     * </ul>
     *
     * <p>支持两种事务模式：</p>
     * <ul>
     *   <li><strong>STRICT（推荐）</strong>: 严格模式，全部成功或全部失败，使用真正的批量操作</li>
     *   <li>LENIENT: 宽松模式，允许部分成功，每个用户独立事务处理</li>
     * </ul>
     *
     * @param vimUsers 原始用户数据
     * @param mode     事务模式（推荐使用 BatchTransactionMode.STRICT）
     * @return 批量操作结果
     */
    public BatchOperationResult<SysUser> batchSaveUsersWithRoles(List<VimUser> vimUsers, BatchTransactionMode mode) {
        if (vimUsers == null || vimUsers.isEmpty()) {
            return BatchOperationResult.<SysUser>builder()
                    .totalCount(0)
                    .successCount(0)
                    .failureCount(0)
                    .build();
        }

        log.info("开始批量保存 {} 个用户，事务模式: {}", vimUsers.size(), mode.getDisplayName());

        try {
            // 1. 数据验证
            BatchOperationResult<SysUser> validationResult = validateUsersForBatchSave(vimUsers);
            if (!validationResult.getErrors().isEmpty() && mode == BatchTransactionMode.STRICT) {
                return validationResult;
            }
            // 2. 数据准备
            BatchUserData batchData = prepareBatchUserData(vimUsers);
            // 3. 根据模式执行不同的保存策略
            if (mode == BatchTransactionMode.STRICT) {
                return executeStrictBatchSave(batchData);
            } else {
                return executeLenientBatchSave(batchData);
            }

        } catch (Exception e) {
            log.error("批量保存用户失败", e);
            BatchOperationResult<SysUser> result = BatchOperationResult.<SysUser>builder()
                    .totalCount(vimUsers.size())
                    .successCount(0)
                    .failureCount(vimUsers.size())
                    .build();
            // 为所有用户添加失败记录
            for (int i = 0; i < vimUsers.size(); i++) {
                VimUser vimUser = vimUsers.get(i);
                String identifier = vimUser.getPhone() != null ? vimUser.getPhone() : "索引:" + i;
                result.addFailureItem(vimUser, "BATCH_SAVE_ERROR", "批量保存用户失败: " + e.getMessage(), e);
            }
            return result;
        }
    }

    /**
     * 验证批量保存用户的数据
     */
    private BatchOperationResult<SysUser> validateUsersForBatchSave(List<VimUser> vimUsers) {
        BatchOperationResult<SysUser> result = BatchOperationResult.<SysUser>builder()
                .totalCount(vimUsers.size())
                .build();

        for (int i = 0; i < vimUsers.size(); i++) {
            VimUser vimUser = vimUsers.get(i);
            String identifier = vimUser.getPhone() != null ? vimUser.getPhone() : "索引:" + i;

            try {
                // 验证必填字段
                if (StrUtil.isBlank(vimUser.getPhone())) {
                    result.addFailureItem(vimUser, identifier, "PHONE_REQUIRED", "手机号不能为空");
                    continue;
                }

                if (StrUtil.isBlank(vimUser.getUsername())) {
                    result.addFailureItem(vimUser, identifier, "USERNAME_REQUIRED", "用户名不能为空");
                    continue;
                }

            } catch (Exception e) {
                result.addFailureItem(vimUser, identifier, "VALIDATION_ERROR",
                        "数据验证失败: " + e.getMessage(), e);
            }
        }

        log.info("数据验证完成，总数: {}, 验证失败: {}", vimUsers.size(), result.getFailureCount());
        return result;
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhoneNumber(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        // 简单的手机号验证：11位数字，以1开头
        return phone.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 准备批量用户数据
     */
    private BatchUserData prepareBatchUserData(List<VimUser> vimUsers) {
        Long tenantId = SecurityUtils.getTenantId();

        // 1. 转换用户数据
        List<SysUser> sysUsers = vimUsers.stream()
                .map(vimUser -> convertVimUserToSysUser(vimUser, tenantId))
                .collect(Collectors.toList());

        // 2. 批量查询已存在的用户（按手机号）
        List<String> phones = vimUsers.stream()
                .map(VimUser::getPhone)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<String, SysUser> existingUsers = batchQueryExistingUsersByPhone(phones, tenantId);

        // 4. 分离新用户和已存在用户
        List<SysUser> newUsers = new ArrayList<>();
        List<VimUser> newVimUsers = new ArrayList<>();

        for (int i = 0; i < vimUsers.size(); i++) {
            VimUser vimUser = vimUsers.get(i);
            SysUser sysUser = sysUsers.get(i);

            if (!existingUsers.containsKey(vimUser.getPhone())) {
                newUsers.add(sysUser);
                newVimUsers.add(vimUser);
            }
        }

        // 5. 收集代理用户（需要特殊处理的用户）
        List<VimUser> agentUsers = newVimUsers.stream()
                .filter(vimUser -> vimUser.getIdentity() == 4)
                .collect(Collectors.toList());

        return BatchUserData.builder()
                .vimUsers(vimUsers)
                .sysUsers(sysUsers)
                .existingUsers(existingUsers)
                .newUsers(newUsers)
                .newVimUsers(newVimUsers)
                .agentUsers(agentUsers)
                .tenantId(tenantId)
                .batchSize(50) // 优化：减小批次大小，提高处理速度
                .build();
    }

    /**
     * 执行严格模式的批量保存（推荐模式，性能最佳）
     *
     * <p><strong>性能优势：</strong></p>
     * <ul>
     *   <li>使用真正的批量操作（insertBatch）</li>
     *   <li>仅需2次数据库写入操作</li>
     *   <li>执行速度快，适合大批量数据</li>
     * </ul>
     *
     * <p><strong>执行流程：</strong></p>
     * <ol>
     *   <li>预处理用户数据（内存操作，设置密码）</li>
     *   <li>批量插入用户（1次数据库操作）</li>
     *   <li>批量分配角色（1次数据库操作）</li>
     *   <li>处理特殊用户类型（代理用户等）</li>
     * </ol>
     *
     * <p><strong>事务特性：</strong>全部成功或全部失败</p>
     */
    @Transactional(rollbackFor = Exception.class)
    private BatchOperationResult<SysUser> executeStrictBatchSave(BatchUserData batchData) {
        BatchOperationResult<SysUser> result = BatchOperationResult.<SysUser>builder()
                .totalCount(batchData.getNewUserCount())
                .build();

        try {
            log.info("执行严格模式批量保存，新用户数量: {}", batchData.getNewUserCount());

            if (batchData.getNewUserCount() == 0) {
                result.setSkipCount(batchData.getExistingUserCount());
                return result;
            }

            // 1. 预处理用户数据（设置密码）
            prepareUsersWithPasswords(batchData.getNewUsers(), batchData.getNewVimUsers());

            // 2. 批量插入用户（包含密码）
            List<SysUser> savedUsers = executeBatchUserInsert(batchData.getNewUsers());

            // 3. 批量分配角色
            executeBatchRoleAssignment(savedUsers, batchData.getNewVimUsers());

            // 4. 处理特殊用户类型（代理用户）
            handleSpecialUserTypes(batchData.getAgentUsers(), savedUsers);

            // 5. 设置成功结果
            savedUsers.forEach(result::addSuccessItem);
            result.setSkipCount(batchData.getExistingUserCount());

            log.info("严格模式批量保存完成，成功: {}, 跳过: {}",
                    result.getSuccessCount(), result.getSkipCount());

            return result;

        } catch (Exception e) {
            log.error("严格模式批量保存失败，事务将回滚", e);
            // 在严格模式下，任何异常都会导致整个事务回滚
            throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED.getCode(),
                    "批量保存用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行宽松模式的批量保存（兼容模式，性能较低）
     *
     * <p><strong>性能特点：</strong></p>
     * <ul>
     *   <li>每个用户在独立事务中处理</li>
     *   <li>需要N×(2+M)次数据库写入操作（N为用户数，M为每用户角色数）</li>
     *   <li>执行速度较慢，适合小批量或数据质量不确定的场景</li>
     * </ul>
     *
     * <p><strong>执行流程：</strong></p>
     * <ol>
     *   <li>预处理用户数据（内存操作，设置密码）</li>
     *   <li>分批处理用户</li>
     *   <li>每个用户在独立事务中：插入用户 + 分配角色</li>
     *   <li>失败隔离，不影响其他用户</li>
     * </ol>
     *
     * <p><strong>事务特性：</strong>允许部分成功，失败隔离</p>
     *
     * <p><strong>建议：</strong>优先使用严格模式（STRICT），性能更佳</p>
     */
    private BatchOperationResult<SysUser> executeLenientBatchSave(BatchUserData batchData) {
        BatchOperationResult<SysUser> result = BatchOperationResult.<SysUser>builder()
                .totalCount(batchData.getNewUserCount())
                .skipCount(batchData.getExistingUserCount())
                .build();

        log.info("执行宽松模式批量保存，新用户数量: {}", batchData.getNewUserCount());

        if (batchData.getNewUserCount() == 0) {
            return result;
        }

        List<SysUser> newUsers = batchData.getNewUsers();
        List<VimUser> newVimUsers = batchData.getNewVimUsers();

        // 预处理用户数据（设置密码）
        prepareUsersWithPasswords(newUsers, newVimUsers);

        // 分批处理，避免单次操作数据量过大
        int batchSize = batchData.getBatchSize();

        for (int i = 0; i < newUsers.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, newUsers.size());
            List<SysUser> batchUsers = newUsers.subList(i, endIndex);
            List<VimUser> batchVimUsers = newVimUsers.subList(i, endIndex);

            processBatchInLenientMode(batchUsers, batchVimUsers, result, i / batchSize + 1);
        }

        log.info("宽松模式批量保存完成，成功: {}, 失败: {}, 跳过: {}",
                result.getSuccessCount(), result.getFailureCount(), result.getSkipCount());

        return result;
    }

    /**
     * 在宽松模式下处理单个批次
     */
    private void processBatchInLenientMode(List<SysUser> batchUsers, List<VimUser> batchVimUsers,
                                           BatchOperationResult<SysUser> result, int batchNumber) {
        log.debug("处理第 {} 批用户，数量: {}", batchNumber, batchUsers.size());

        for (int i = 0; i < batchUsers.size(); i++) {
            SysUser user = batchUsers.get(i);
            VimUser vimUser = batchVimUsers.get(i);
            String identifier = vimUser.getPhone();

            try {
                // 在独立事务中保存单个用户
                SysUser savedUser = saveUserInSeparateTransaction(user, vimUser);
                if (savedUser != null) {
                    result.addSuccessItem(savedUser);
                    log.debug("用户保存成功: {}", identifier);
                } else {
                    result.addFailureItem(vimUser, identifier, "SAVE_FAILED", "用户保存失败");
                }
            } catch (Exception e) {
                log.warn("保存用户失败: {}", identifier, e);
                result.addFailureItem(vimUser, identifier, "SAVE_ERROR",
                        "保存用户时发生异常: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 在独立事务中保存单个用户（用于宽松模式）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW,
            rollbackFor = Exception.class)
    private SysUser saveUserInSeparateTransaction(SysUser user, VimUser vimUser) {
        try {
            // 0. 预设置密码（优化：在插入前设置密码）
            if (user.getPassword() == null) {
                String password = generatePasswordByPhone(vimUser.getPhone());
                String encodedPassword = passwordEncoder.encode(password);
                user.setPassword(encodedPassword);
                user.setPasswordUpdateTime(TimeUtil.now());
            }

            // 1. 插入用户（包含密码）
            int insertResult = baseMapper.insertSelective(user);
            if (insertResult <= 0) {
                return null;
            }

            // 2. 获取用户ID（如果自动生成失败）
            if (user.getId() == null) {
                SysUser insertedUser = baseMapper.selectByPhone(vimUser.getPhone(), user.getTenantId());
                if (insertedUser != null) {
                    user.setId(insertedUser.getId());
                } else {
                    return null;
                }
            }

            // 3. 分配角色（密码已在插入时设置，无需额外更新）
            assignRoleByIdentity(user.getId(), vimUser.getIdentity());

            // 4. 处理特殊用户类型
            if (vimUser.getIdentity() == 4) {
                createAgentDeptAndAssignUsers(vimUser, user.getId());
            }

            return user;

        } catch (Exception e) {
            log.error("在独立事务中保存用户失败: {}", vimUser.getPhone(), e);
            throw e;
        }
    }

    /**
     * 预处理用户数据，设置密码（优化：在插入前设置密码，避免后续更新操作）
     */
    private void prepareUsersWithPasswords(List<SysUser> users, List<VimUser> vimUsers) {
        if (users.size() != vimUsers.size()) {
            throw new IllegalArgumentException("用户列表和VIM用户列表大小不匹配");
        }

        log.debug("开始预处理 {} 个用户的密码", users.size());

        for (int i = 0; i < users.size(); i++) {
            SysUser user = users.get(i);
            VimUser vimUser = vimUsers.get(i);

            // 生成密码
            String password = generatePasswordByPhone(vimUser.getPhone());
            String encodedPassword = passwordEncoder.encode(password);

            // 直接设置到用户对象中
            user.setPassword(encodedPassword);
            user.setPasswordUpdateTime(TimeUtil.now());
        }

        log.debug("用户密码预处理完成");
    }

    /**
     * 批量插入用户（已包含密码，无需后续更新）
     *
     * 优化说明：
     * 1. 使用指定ID插入，避免自动生成ID的问题
     * 2. 简化ID验证逻辑，因为ID已预先设置
     * 3. 提供更详细的错误信息用于问题排查
     */
    private List<SysUser> executeBatchUserInsert(List<SysUser> users) {
        if (users.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始批量插入 {} 个用户（包含密码和指定ID）", users.size());

        // 验证所有用户都有预设的ID
        for (SysUser user : users) {
            if (user.getId() == null) {
                throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED,
                        "批量插入前用户ID为空，请检查VimUser转换逻辑: " + user.getUsername());
            }
        }

        // 使用MyBatis-Flex的批量插入（用户数据已包含加密密码和指定ID）
        int insertCount = baseMapper.insertBatch(users);

        if (insertCount != users.size()) {
            throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED,
                    String.format("批量插入用户失败，期望插入 %d 个，实际插入 %d 个", users.size(), insertCount));
        }

        // 验证插入后ID仍然存在（理论上应该保持不变）
        for (SysUser user : users) {
            if (user.getId() == null) {
                throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED,
                        "批量插入后用户ID丢失，这不应该发生: " + user.getUsername());
            }
        }

        log.debug("批量插入用户完成，成功插入 {} 个用户（包含密码和指定ID）", insertCount);
        return users;
    }

    /**
     * 批量更新密码（已废弃 - 现在密码在插入时就设置了）
     *
     * @deprecated 密码现在在 prepareUsersWithPasswords() 方法中预设置，插入时就包含密码，无需后续更新
     */
    @Deprecated
    private void executeBatchPasswordGeneration(List<SysUser> users, List<VimUser> vimUsers) {
        log.warn("executeBatchPasswordGeneration 方法已废弃，密码现在在插入前预设置");
        // 此方法已不再使用，密码在 prepareUsersWithPasswords() 中预设置
        // 保留此方法仅为向后兼容，实际不执行任何操作
    }

    /**
     * 批量分配角色
     */
    private void executeBatchRoleAssignment(List<SysUser> users, List<VimUser> vimUsers) {
        if (users.size() != vimUsers.size()) {
            throw new IllegalArgumentException("用户列表和VIM用户列表大小不匹配");
        }

        log.debug("开始批量分配 {} 个用户的角色", users.size());

        List<SysUserRole> userRoles = new ArrayList<>();

        for (int i = 0; i < users.size(); i++) {
            SysUser user = users.get(i);
            VimUser vimUser = vimUsers.get(i);

            List<Long> roleIds = getRoleIdsByIdentity(vimUser.getIdentity());

            for (Long roleId : roleIds) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(roleId);
                userRole.setTenantId(user.getTenantId());
                userRole.setCreateBy(SecurityUtils.getUserId());
                userRole.setCreateTime(TimeUtil.now());
                userRole.setUpdateBy(SecurityUtils.getUserId());
                userRole.setUpdateTime(TimeUtil.now());

                userRoles.add(userRole);
            }
        }

        if (!userRoles.isEmpty()) {
            int insertCount = userRoleMapper.insertBatch(userRoles);
            log.debug("批量分配角色完成，成功分配 {} 个角色关联", insertCount);
        }
    }

    /**
     * 处理特殊用户类型（如代理用户）
     */
    private void handleSpecialUserTypes(List<VimUser> agentUsers, List<SysUser> savedUsers) {
        if (agentUsers.isEmpty()) {
            return;
        }

        log.debug("开始处理 {} 个代理用户的特殊逻辑", agentUsers.size());

        // 创建VIM用户到系统用户的映射
        Map<String, SysUser> phoneToUserMap = savedUsers.stream()
                .collect(Collectors.toMap(SysUser::getPhone, Function.identity()));

        for (VimUser agentUser : agentUsers) {
            SysUser sysUser = phoneToUserMap.get(agentUser.getPhone());
            if (sysUser != null) {
                try {
                    createAgentDeptAndAssignUsers(agentUser, sysUser.getId());
                    log.debug("代理用户特殊处理完成: {}", agentUser.getPhone());
                } catch (Exception e) {
                    log.error("处理代理用户失败: {}", agentUser.getPhone(), e);
                    // 在严格模式下，这里会抛出异常导致事务回滚
                    // 在宽松模式下，这个异常已经在上层被捕获
                    throw e;
                }
            }
        }
    }

    /**
     * 根据身份获取角色ID列表
     */
    private List<Long> getRoleIdsByIdentity(Integer identity) {
        List<Long> roleIds = new ArrayList<>();

        switch (identity) {
            case 1: // 普通用户
                roleIds.add(3L); // 普通用户角色ID
                break;
            case 2: // VIP用户
                roleIds.add(4L); // VIP用户角色ID
                break;
            case 3: // 主播
                roleIds.add(5L); // 主播角色ID
                break;
            case 4: // 代理
                roleIds.add(6L); // 代理角色ID
                break;
            default:
                log.warn("未知的身份类型: {}", identity);
                break;
        }

        return roleIds;
    }

    /**
     * 根据手机号生成密码
     */
    private String generatePasswordByPhone(String phone) {
        // 使用手机号后6位作为初始密码
        return phone.substring(phone.length() - 6) + "voltskins";
    }




    @Override
    public List<VimUser> queryUsersForSync() {
        return queryAnchorAndAgentUsersFromSlave();
    }

    @Override
    public Map<String, SysUser> batchQueryExistingUsersByPhone(List<String> phones, Long tenantId) {
        if (phones.isEmpty()) {
            return Map.of();
        }

        try {
            // 使用 QueryColumn.in() 方法
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(SysUser::getPhone).in(phones)
                    .and(SysUser::getTenantId).eq(tenantId);

            List<SysUser> existingUsers = baseMapper.selectListByQuery(queryWrapper);
            return existingUsers.stream()
                    .collect(Collectors.toMap(SysUser::getPhone, Function.identity()));
        } catch (Exception e) {
            log.error("批量查询用户手机号失败", e);
            return Map.of();
        }
    }

    /**
     * 从从库查询用户（非事务方法）
     * 使用MyBatis-Flex官方多数据源功能
     *
     * @return 用户列表
     */
    private List<VimUser> queryAnchorAndAgentUsersFromSlave() {
        try {
            // 使用MyBatis-Flex官方数据源切换方式
            return DataSourceKey.use("slave", () -> {
                log.debug("使用MyBatis-Flex切换到从库查询用户");
                return vimUserMapper.selectAnchorAndAgentUsers();
            });
        } catch (Exception e) {
            log.error("查询从库vim_user表失败，错误详情: {}", e.getMessage(), e);
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR,
                    "查询从库vim_user表失败: " + e.getMessage());
        }
    }

    /**
     * 从从库查询代理的下级主播
     * 使用MyBatis-Flex官方多数据源功能
     * @param agentId 代理ID
     * @return 下级主播列表
     */
    private List<VimUser> querySubAnchorsByAgentId(Integer agentId) {
        try {
            // 使用MyBatis-Flex官方数据源切换方式
            return DataSourceKey.use("slave", () -> {
                log.debug("使用MyBatis-Flex切换到从库查询代理下级主播: agentId={}", agentId);
                return vimUserMapper.selectUsersByInviteUser(agentId);
            });
        } catch (Exception e) {
            log.error("查询从库vim_user表失败: agentId={}, 错误详情: {}", agentId, e.getMessage(), e);
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR,
                    "查询从库vim_user表失败: " + e.getMessage());
        }
    }

    /**
     * 检查主库中是否存在指定手机号的用户（非事务方法）
     *
     * @param phone    手机号
     * @param tenantId 租户ID
     * @return 用户信息，不存在则返回null
     */
    private SysUser checkUserExistsByPhone(String phone, Long tenantId) {
        try {
            log.debug("使用主库查询用户: phone={}, tenantId={}", phone, tenantId);

            // 直接使用baseMapper查询，@DataSource注解会确保使用主库
            SysUser user = baseMapper.selectByPhone(phone, tenantId);
            log.debug("查询结果: {}", user != null ? "找到用户" : "未找到用户");
            return user;
        } catch (Exception e) {
            log.error("查询主库用户失败: phone={}, tenantId={}", phone, tenantId, e);
            throw new RuntimeException("查询用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存用户到主库（单独事务）
     *
     * @param user 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    private void saveUserToMaster(SysUser user) {
        try {
            log.debug("保存用户到主库: username={}", user.getUsername());
            boolean saved = save(user);
            if (!saved) {
                throw new RuntimeException("保存用户失败");
            }
        } catch (Exception e) {
            log.error("保存用户到主库失败: username={}", user.getUsername(), e);
            throw e;
        }
    }

    /**
     * 检查主库中是否存在指定用户名的用户
     *
     * @param username 用户名
     * @param tenantId 租户ID
     * @return 用户信息，不存在则返回null
     */
    private SysUser checkUserExistsByUsername(String username, Long tenantId) {
        try {
            log.debug("使用主库查询用户名: username={}, tenantId={}", username, tenantId);

            // 直接使用baseMapper查询，@DataSource注解会确保使用主库
            SysUser user = baseMapper.selectByUsername(username, tenantId);
            log.debug("用户名查询结果: {}", user != null ? "找到用户" : "未找到用户");
            return user;
        } catch (Exception e) {
            log.error("查询主库用户名失败: username={}, tenantId={}", username, tenantId, e);
            throw new RuntimeException("查询用户名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将VimUser转换为SysUser
     * @param vimUser  vim_user表的用户数据
     * @param tenantId 租户ID
     * @return 转换后的SysUser对象
     */
    private SysUser convertVimUserToSysUser(VimUser vimUser, Long tenantId) {
        SysUser sysUser = new SysUser();

        // 直接使用VimUser的ID作为SysUser的ID
        if (vimUser.getId() != null) {
            sysUser.setId(vimUser.getId().longValue());
        }
        // 基本信息映射
        sysUser.setTenantId(tenantId);
        // 使用手机号作为用户名，确保唯一性和便于管理
        sysUser.setUsername(vimUser.getPhone());

        // 设置临时密码，稍后会根据用户ID生成正式密码
        sysUser.setPassword(passwordEncoder.encode("temp_password_" + System.currentTimeMillis()));
        sysUser.setNickname(vimUser.getNickname());
        sysUser.setRealName(vimUser.getNickname()); // 使用昵称作为真实姓名
        sysUser.setAvatar(vimUser.getUserimage());
        sysUser.setPhone(vimUser.getPhone());
        sysUser.setLastLoginIp(vimUser.getLastLoginIp());
        // 状态映射：vim_user.state (1=正常,2=禁用) -> sys_user.status (1=启用,0=禁用)
        sysUser.setStatus(vimUser.getState() == 1 ? 1 : 0);
        // 默认值设置
        sysUser.setIsAdmin(0); // 非管理员
        sysUser.setGender(0); // 未知性别
        // 根据身份类型设置备注
        String identityDesc = switch (vimUser.getIdentity()) {
            case 1 -> "普通用户";
            case 2 -> "线上主播";
            case 3 -> "线下主播";
            case 4 -> "代理";
            default -> "未知身份";
        };
        sysUser.setRemark("从vim_user同步的" + identityDesc + "用户 (vim_user_id=" + vimUser.getId() + ", identity=" + vimUser.getIdentity() + ")");
        // 时间转换：int时间戳 -> Long时间戳
        if (vimUser.getLastLoginTime() != null && vimUser.getLastLoginTime() > 0) {
            sysUser.setLastLoginTime(vimUser.getLastLoginTime().longValue());
        }

        if (vimUser.getCreateTime() != null && vimUser.getCreateTime() > 0) {
            Long createTime = vimUser.getCreateTime().longValue();
            sysUser.setCreateTime(createTime);
            sysUser.setUpdateTime(createTime);
        }

        return sysUser;
    }

    /**
     * 生成并更新用户密码
     * 密码规则：手机号 + "@" + 用户ID
     *
     * @param userId 用户ID
     * @param phone  手机号
     */
    private void generateAndUpdatePassword(Long userId, String phone) {
        try {
            // 生成密码：手机号 + "@" + 用户ID
            String rawPassword = phone + "@" + userId;
            String encodedPassword = passwordEncoder.encode(rawPassword);

            // 更新用户密码
            SysUser updateUser = new SysUser();
            updateUser.setId(userId);
            updateUser.setPassword(encodedPassword);

            boolean updated = updateById(updateUser);
            if (updated) {
                log.info("用户密码生成成功: userId={}, phone={}, password={}", userId, phone, rawPassword);
            } else {
                log.error("用户密码更新失败: userId={}", userId);
            }
        } catch (Exception e) {
            log.error("生成并更新用户密码失败: userId={}, phone={}", userId, phone, e);
        }
    }

    /**
     * 根据用户身份自动分配角色
     *
     * @param userId   用户ID
     * @param identity 用户身份类型
     */
    private void assignRoleByIdentity(Long userId, Integer identity) {
        try {
            String roleCode = switch (identity) {
                case 2, 3 -> CommonConstants.ANCHOR_ROLE;  // 主播角色
                case 4 -> CommonConstants.AGENT_ROLE;      // 代理角色
                default -> CommonConstants.USER_ROLE;      // 默认用户角色
            };

            // 查询角色ID
            SysRole role = roleService.getRoleByCode(roleCode);
            if (role != null) {
                assignRoles(userId, List.of(role.getId()));
                log.info("自动分配角色成功: userId={}, roleCode={}, roleName={}", userId, roleCode, role.getRoleName());
            } else {
                log.warn("角色不存在，无法自动分配: roleCode={}", roleCode);
            }
        } catch (Exception e) {
            log.error("自动分配角色失败: userId={}, identity={}", userId, identity, e);
        }
    }

    /**
     * 为代理创建专属部门并分配用户
     *
     * @param agent       代理用户信息
     * @param agentUserId 代理用户ID
     */
    @Override
    public void createAgentDeptAndAssignUsers(VimUser agent, Long agentUserId) {
        try {
            // 1. 为代理创建专属部门
            String deptCode = "AGENT_" + agent.getId();
            String deptName = "代理-" + agent.getNickname();

            SysDept agentDept = createDeptIfNotExists(deptCode, deptName);

            // 2. 将代理分配到自己的部门
            deptPermissionService.addUserDept(agentUserId, agentDept.getId(), true);
            log.info("代理分配到专属部门成功: agentId={}, deptId={}", agentUserId, agentDept.getId());
            // 3. 查找并分配下级主播到该部门
            assignSubAnchorsToAgentDept(agent.getId(), agentDept.getId());
        } catch (Exception e) {
            log.error("创建代理部门和分配用户失败: agentId={}", agentUserId, e);
        }
    }

    /**
     * 创建部门（如果不存在）
     *
     * @param deptCode 部门编码
     * @param deptName 部门名称
     * @return 部门信息
     */
    private SysDept createDeptIfNotExists(String deptCode, String deptName) {
        // 先查询是否已存在
        SysDept existingDept = deptService.getDeptByCode(deptCode);
        if (existingDept != null) {
            return existingDept;
        }

        // 创建新部门
        DeptCreateRequest createRequest = new DeptCreateRequest();
        createRequest.setParentId(1L); // 设置为总公司的子部门
        createRequest.setDeptCode(deptCode);
        createRequest.setDeptName(deptName);
        createRequest.setStatus(1);
        createRequest.setSortOrder(100);
        createRequest.setRemark("系统自动创建的代理部门");

        boolean success = deptService.createDept(createRequest);
        if (success) {
            log.info("创建代理部门成功: deptCode={}, deptName={}", deptCode, deptName);
            return deptService.getDeptByCode(deptCode);
        } else {
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR, "创建代理部门失败: " + deptCode);
        }
    }

    /**
     * 将下级主播分配到代理部门
     *
     * @param agentId 代理ID
     * @param deptId  部门ID
     */
    private void assignSubAnchorsToAgentDept(Integer agentId, Long deptId) {
        try {
            // 查找所有inviteUser=agentId的主播（使用从库）
            List<VimUser> subAnchors = querySubAnchorsByAgentId(agentId);

            if (subAnchors.isEmpty()) {
                log.debug("代理 {} 没有下级主播", agentId);
                return;
            }

            // 优化：批量查询对应的sys_user，避免N+1问题
            List<String> phones = subAnchors.stream()
                    .map(VimUser::getPhone)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            Long tenantId = SecurityUtils.getTenantId();
            Map<String, SysUser> phoneToUserMap = batchQueryExistingUsersByPhone(phones, tenantId);

            // 收集需要分配的用户ID
            List<Long> userIdsToAssign = new ArrayList<>();
            for (VimUser subAnchor : subAnchors) {
                SysUser sysUser = phoneToUserMap.get(subAnchor.getPhone());
                if (sysUser != null) {
                    userIdsToAssign.add(sysUser.getId());
                    log.debug("准备分配主播到代理部门: anchorId={}, agentDeptId={}", sysUser.getId(), deptId);
                }
            }

            // 优化：使用批量分配方法，避免循环单次更新
            if (!userIdsToAssign.isEmpty()) {
                try {
                    int successCount = deptPermissionService.batchAssignUsersToDept(deptId, userIdsToAssign, false);
                    log.info("批量分配下级用户到代理部门成功: deptId={}, 总数={}, 成功={}",
                            deptId, userIdsToAssign.size(), successCount);
                } catch (Exception e) {
                    log.error("批量分配下级用户到代理部门失败: deptId={}, userIds={}", deptId, userIdsToAssign, e);
                    // 不抛出异常，避免影响主流程
                }
            }

            log.info("完成下级主播分配: agentId={}, 查找到下级={}, 成功分配={}",
                    agentId, subAnchors.size(), userIdsToAssign.size());
        } catch (Exception e) {
            log.error("分配下级主播到代理部门失败: agentId={}, deptId={}", agentId, deptId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    public List<SysUser> listByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return List.of();
        }

        try {
            Long tenantId = SecurityUtils.getTenantId();

            // 使用批量查询方法
            List<SysUser> users = baseMapper.selectByIds(userIds, tenantId);
            log.debug("批量查询用户: 请求{}个用户ID，返回{}个用户", userIds.size(), users.size());

            return users != null ? users : List.of();
        } catch (Exception e) {
            log.error("批量查询用户失败: userIds={}", userIds, e);
            return List.of();
        }
    }

    @Override
    public void clearUserRelatedCaches(Long userId, Long tenantId) {
        try {
            // 清理用户相关的所有缓存
            if (cacheManager != null) {
                // 清理用户基本信息缓存
                cacheManager.getCache("userCache").evictIfPresent("id:" + userId + ":tenant:" + tenantId);

                // 清理用户权限缓存
                cacheManager.getCache("userPermissions").evictIfPresent(userId + ":" + tenantId);

                // 清理用户角色缓存
                cacheManager.getCache("userRoles").evictIfPresent(userId + ":" + tenantId);

                log.debug("清理用户相关缓存完成: userId={}, tenantId={}", userId, tenantId);
            }
        } catch (Exception e) {
            log.warn("清理用户相关缓存失败: userId={}, tenantId={}", userId, tenantId, e);
        }
    }


}