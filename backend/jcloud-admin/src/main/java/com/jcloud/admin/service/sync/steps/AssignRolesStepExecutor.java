package com.jcloud.admin.service.sync.steps;

import com.jcloud.admin.service.SysRoleService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepExecutor;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.dto.UserSyncTaskStatus;
import com.jcloud.common.entity.SysRole;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.SysUserRole;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.mapper.SysUserRoleMapper;
import com.jcloud.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量分配角色步骤执行器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AssignRolesStepExecutor implements StepExecutor<AssignRolesStepExecutor.AssignRolesResult> {

    private final SysUserService sysUserService;
    private final SysRoleService sysRoleService;
    private final SysUserRoleMapper userRoleMapper;

    @Override
    public UserSyncTaskStatus.SyncStep getStepType() {
        return UserSyncTaskStatus.SyncStep.ASSIGN_ROLES;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StepResult<AssignRolesResult> execute(StepContext context) throws Exception {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行批量分配角色步骤，任务ID: {}", context.getTaskId());

            // 1. 从上下文获取数据
            @SuppressWarnings("unchecked")
            List<SysUser> savedUsers = context.getSharedData("savedUsers", List.class);
            @SuppressWarnings("unchecked")
            List<VimUser> newVimUsers = context.getSharedData("newVimUsers", List.class);

            if (savedUsers == null || savedUsers.isEmpty() || newVimUsers == null || newVimUsers.isEmpty()) {
                return StepResult.success(
                        new AssignRolesResult(new ArrayList<>()),
                        "没有需要分配角色的用户",
                        0, 0
                );
            }

            if (savedUsers.size() != newVimUsers.size()) {
                throw new IllegalArgumentException("保存用户列表和VIM用户列表大小不匹配");
            }

            // 2. 预查询所有需要的角色
            Map<String, SysRole> roleMap = preloadRoles();

            // 3. 批量分配角色
            List<SysUserRole> userRoles = executeBatchRoleAssignment(savedUsers, newVimUsers, roleMap);

            // 4. 保存结果到上下文
            context.setSharedData("userRoles", userRoles);

            long duration = System.currentTimeMillis() - startTime;

            AssignRolesResult result = new AssignRolesResult(userRoles);

            String message = String.format("批量分配角色完成，分配角色关联: %d 个", userRoles.size());

            log.info("批量分配角色步骤完成，任务ID: {}, 耗时: {}ms, 结果: {}",
                    context.getTaskId(), duration, message);

            return StepResult.<AssignRolesResult>builder()
                    .success(true)
                    .data(result)
                    .message(message)
                    .totalCount(savedUsers.size())
                    .successCount(userRoles.size())
                    .duration(duration)
                    .build();

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("批量分配角色步骤执行失败，任务ID: {}, 耗时: {}ms", context.getTaskId(), duration, e);
            return StepResult.failure("批量分配角色失败: " + e.getMessage(), e.toString());
        }
    }

    /**
     * 预加载所有需要的角色
     */
    private Map<String, SysRole> preloadRoles() {
        Map<String, SysRole> roleMap = new HashMap<>();

        // 预加载常用角色
        String[] roleCodes = {
                // 普通用户角色
                CommonConstants.USER_ROLE,
                // 主播角色
                CommonConstants.ANCHOR_ROLE,
                // 代理角色
                CommonConstants.AGENT_ROLE
        };

        for (String roleCode : roleCodes) {
            SysRole role = sysRoleService.getRoleByCode(roleCode);
            if (role != null) {
                roleMap.put(roleCode, role);
            } else {
                log.warn("角色不存在: {}", roleCode);
            }
        }

        log.debug("预加载角色完成，角色数量: {}", roleMap.size());
        return roleMap;
    }

    /**
     * 批量分配角色
     */
    private List<SysUserRole> executeBatchRoleAssignment(List<SysUser> users, List<VimUser> vimUsers,
                                                         Map<String, SysRole> roleMap) {
        List<SysUserRole> userRoles = new ArrayList<>();
        Long tenantId = SecurityUtils.getTenantId();
        Long currentUserId = SecurityUtils.getUserId();

        for (int i = 0; i < users.size(); i++) {
            SysUser user = users.get(i);
            VimUser vimUser = vimUsers.get(i);

            // 根据身份获取角色
            String roleCode = getRoleCodeByIdentity(vimUser.getIdentity());
            SysRole role = roleMap.get(roleCode);

            if (role != null) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(role.getId());
                userRole.setTenantId(tenantId);
                userRole.setCreateBy(currentUserId);

                userRoles.add(userRole);

                log.debug("为用户分配角色: userId={}, roleCode={}, roleName={}",
                        user.getId(), roleCode, role.getRoleName());
            } else {
                log.warn("角色不存在，无法分配: roleCode={}, userId={}", roleCode, user.getId());
            }
        }

        // 批量插入用户角色关联
        if (!userRoles.isEmpty()) {
            int insertCount = userRoleMapper.insertBatch(userRoles);
            log.info("批量插入用户角色关联完成，插入数量: {}", insertCount);
        }

        return userRoles;
    }

    /**
     * 根据用户身份获取角色代码
     */
    private String getRoleCodeByIdentity(Integer identity) {
        if (identity == null) {
            // 默认用户角色
            return CommonConstants.USER_ROLE;
        }

        return switch (identity) {
            // 主播角色（线上主播、线下主播）
            case 2, 3 -> CommonConstants.ANCHOR_ROLE;
            // 代理角色
            case 4 -> CommonConstants.AGENT_ROLE;
            // 默认用户角色
            default -> CommonConstants.USER_ROLE;
        };
    }

    /**
     * 批量分配角色结果
     *
     * @param userRoles Getters
     */
    public record AssignRolesResult(List<SysUserRole> userRoles) {
    }
}
